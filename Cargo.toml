[workspace]
default-members = ["crates/embucketd"]
members = [
  "crates/embucketd",
  "crates/embucket-seed",
  "crates/api-iceberg-rest",
  "crates/api-internal-rest",
  "crates/api-snowflake-rest",
  "crates/api-ui",
  "crates/api-ui-static-assets",
  "crates/embucket-functions",
  "crates/df-catalog",
  "crates/core-executor",
  "crates/core-history",
  "crates/core-metastore",
  "crates/core-utils",
  "crates/api-sessions",
]
resolver = "2"
package.license-file = "LICENSE"

[profile.test]
incremental = true
opt-level = 1
debug = true
codegen-units = 16

[workspace.dependencies]
async-trait = { version = "0.1.84" }
aws-config = { version = "1.5.17" }
aws-credential-types = { version = "1.2.1",  features = ["hardcoded-credentials"]}
axum = { version = "0.8.1", features = ["multipart", "macros"] }
axum-macros = "0.5"
bytes = { version = "1.8.0" }
chrono = { version = "0.4.41", default-features = false, features = ["serde",  "clock"] }
dashmap = "6.1.0"
datafusion = { version = "47.0.0" }
datafusion-common = { version = "47.0.0" }
datafusion-doc = { version = "47.0.0" }
datafusion-expr = { version = "47.0.0" }
datafusion-functions-json = { git = "https://github.com/Embucket/datafusion-functions-json.git", rev = "4a431bc89fb88731685609618799d392cb0a74e7" }
datafusion-macros = { version = "47.0.0" }
datafusion-physical-plan = { version = "47.0.0" }
datafusion_iceberg = { git = "https://github.com/Embucket/iceberg-rust.git", rev = "7f7e21f0bcf50f6fe6c13cada6b76057ffdfa869" }
futures = { version = "0.3" }
http = "1.2"
http-body-util = "0.1.0"
iceberg = { git = "https://github.com/apache/iceberg-rust.git", rev="7a5ad1fcaf00d4638857812bab788105f6c60573"}
iceberg-rest-catalog = { git = "https://github.com/Embucket/iceberg-rust.git", rev = "7f7e21f0bcf50f6fe6c13cada6b76057ffdfa869" }
iceberg-rust = { git = "https://github.com/Embucket/iceberg-rust.git", rev = "7f7e21f0bcf50f6fe6c13cada6b76057ffdfa869" }
iceberg-rust-spec = { git = "https://github.com/Embucket/iceberg-rust.git", rev = "7f7e21f0bcf50f6fe6c13cada6b76057ffdfa869" }
iceberg-s3tables-catalog = { git = "https://github.com/Embucket/iceberg-rust.git", rev = "7f7e21f0bcf50f6fe6c13cada6b76057ffdfa869" }
indexmap = "2.7.1"
jsonwebtoken = "9.3.1"
lazy_static = { version = "1.5" }
# Should be updated to 0.12.0 after slate db update
object_store = { version = "0.12.0", features = ["aws", "gcp", "azure"] }
regex = "1.11"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
slatedb = { version = "0.6.1" }
snafu = { version = "0.8.5", features = ["futures"] }
snmalloc-rs = { version = "0.3" }
strum = { version = "0.26.3", features = ["derive"] }
tar = "0.4.44"
tempfile = { version = "3" }
time = "0.3.37"
tokio = { version = "1", features = ["full", "tracing"] }
tower = { version = "0.5", features = ["util"] }
tower-http = { version = "0.6.1", features = [
  "catch-panic",
  "timeout",
  "sensitive-headers",
  "cors",
  "trace",
] }
tower-sessions = { version = "0.14.0" }
tracing = { version = "0.1", features = ["attributes"] }
tracing-attributes = { version = "0.1.28" }
url = "2.5"
utoipa = { version = "5.3.1", features = ["uuid", "chrono"] }
utoipa-axum = { version = "0.2.0" }
utoipa-swagger-ui = { version = "9", features = ["axum"] }
uuid = { version = "1.10.0", features = ["v4", "serde"] }
validator = { version = "0.20.0", features = ["derive"] }

[patch.crates-io]
datafusion = { git = "https://github.com/Embucket/datafusion.git", rev = "33cb7accb32a9ebb30a94f602fd4059eeb2d98f4" }
datafusion-common = { git = "https://github.com/Embucket/datafusion.git", rev = "33cb7accb32a9ebb30a94f602fd4059eeb2d98f4" }
datafusion-expr = { git = "https://github.com/Embucket/datafusion.git", rev = "33cb7accb32a9ebb30a94f602fd4059eeb2d98f4" }
datafusion-physical-plan = { git = "https://github.com/Embucket/datafusion.git", rev = "33cb7accb32a9ebb30a94f602fd4059eeb2d98f4" }
datafusion-doc = { git = "https://github.com/Embucket/datafusion.git", rev = "33cb7accb32a9ebb30a94f602fd4059eeb2d98f4" }
datafusion-macros = { git = "https://github.com/Embucket/datafusion.git", rev = "33cb7accb32a9ebb30a94f602fd4059eeb2d98f4" }

[workspace.lints.clippy]
all = { level = "deny", priority = -1 }
pedantic = { level = "warn", priority = 1 }
as_conversions = "warn"
nursery = { level = "warn", priority = 1 }
unwrap_used = "deny"
expect_used = "deny"
print_stdout = "deny"
similar_names = { level = "allow", priority = 2 }

# These should be removed eventually
missing_errors_doc = { level = "allow", priority = 2 }
missing_panics_doc = { level = "allow", priority = 2 }
significant_drop_tightening = { level = "allow", priority = 2 }
module_name_repetitions = { level = "allow", priority = 2 }
option_if_let_else = { level = "allow", priority = 2 }
