query TT
SELECT ROUND(135.135), ROUND(-975.975)
----
135	-976

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE test_ceiling(n FLOAT, scale INT);

exclude-from-coverage
statement ok
INSERT INTO test_ceiling (n, scale) VALUES
    (-975.975, -1),
    (-975.975, 0),
    (-975.975, 2),
    (135.135, -2),
    (135.135, 0),
    (135.135, 1),
    (135.135, 3),
    (135.135, 50),
    (135.135, NULL);

query TTT
SELECT n, scale, ROUND(n, scale)
  FROM test_ceiling
  ORDER BY n, scale
----
-975.975	-1	-980.0
-975.975	0	-976.0
-975.975	2	-975.98
135.135	-2	100.0
135.135	0	135.0
135.135	1	135.1
135.135	3	135.135
135.135	50	135.135
135.135	NULL	NULL

query TT
SELECT ROUND(2.5, 0), ROUND(2.5, 0, 'HALF_TO_EVEN')
----
3	2

query TT
SELECT ROUND(-2.5, 0), ROUND(2.5, 0, 'HALF_TO_EVEN')
----
-3	2

query T
SELECT ROUND(
  EXPR => -2.5,
  SCALE => 0)
----
-3

query T
SELECT ROUND(
  EXPR => -2.5,
  SCALE => 0,
  ROUNDING_MODE => 'HALF_TO_EVEN')
----
-2

exclude-from-coverage
statement ok
CREATE OR REPLACE TEMP TABLE rnd1(f float, d DECIMAL(10, 3));

exclude-from-coverage
statement ok
INSERT INTO rnd1 (f, d) VALUES
      ( -10.005,  -10.005),
      (  -1.005,   -1.005),
      (   1.005,    1.005),
      (  10.005,   10.005)
      ;

query TTTT
select f, round(f, 2), 
       d, round(d, 2) 
    from rnd1 
    order by 1
----
-10.005	-10.01	-10.005	-10.01
-1.005	-1.0	-1.005	-1.01
1.005	1.0	1.005	1.01
10.005	10.01	10.005	10.01

