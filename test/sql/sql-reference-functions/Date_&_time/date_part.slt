query T
SELECT DATE_PART(quarter, '2024-04-08'::DATE)
----
2

query TT
SELECT TO_TIMESTAMP(
  '2024-04-08T23:39:20.123-07:00') AS "TIME_STAMP1",
  DATE_PART(year, TO_TIMESTAMP('2024-04-08T23:39:20.123-07:00')) AS "EXTRACTED YEAR"
----
'2024-04-08T23:39:20.123000'	2024

query TT
SELECT TO_TIMESTAMP(
  '2024-04-08T23:39:20.123-07:00') AS "TIME_STAMP1",
  DATE_PART(epoch_second, TO_TIMESTAMP('2024-04-08T23:39:20.123-07:00')) AS "EXTRACTED EPOCH SECOND"
----
'2024-04-08T23:39:20.123000'	1712619560

query TT
SELECT TO_TIMESTAMP(
  '2024-04-08T23:39:20.123-07:00') AS "TIME_STAMP1",
  DATE_PART(epoch_millisecond, TO_TIMESTAMP('2024-04-08T23:39:20.123-07:00')) AS "EXTRACTED EPOCH MILLISECOND"
----
'2024-04-08T23:39:20.123000'	1712619560123

