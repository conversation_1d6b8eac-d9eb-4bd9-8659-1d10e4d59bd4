query T
SELECT STRTOK('a.b.c', '.', 1)
----
a

query T
SELECT STRTOK('<EMAIL>', '@.', 1)
----
user

query T
SELECT STRTOK('<EMAIL>', '@.', 2)
----
snowflake

query T
SELECT STRTOK('<EMAIL>', '@.', 3)
----
com

query T
select strtok('<EMAIL>.', '@.', 4)
----
NULL

query T
select strtok('', '', 1)
----
NULL

query T
select strtok('a.b', '', 1)
----
a.b

query T
select strtok(NULL, '.', 1)
----
NULL

query T
select strtok('a.b', NULL, 1)
----
NULL

query T
select strtok('a.b', '.', NULL)
----
NULL

