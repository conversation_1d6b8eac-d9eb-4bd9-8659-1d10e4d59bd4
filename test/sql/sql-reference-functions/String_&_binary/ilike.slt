exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE ilike_ex(name VA<PERSON>HA<PERSON>(20));

exclude-from-coverage
statement ok
INSERT INTO ilike_ex VALUES
  ('<PERSON>'),
  ('<PERSON>'),
  ('<PERSON>_down'),
  ('<PERSON> down'),
  (null);

query T
SELECT * 
  FROM ilike_ex 
  WHERE name ILIKE '%j%h%do%'
  ORDER BY 1
----
<PERSON>_down

query T
SELECT *
  FROM ilike_ex
  WHERE name NOT ILIKE '%j%h%do%'
  ORDER BY 1
----
<PERSON>
Joe down

query T
SELECT * 
  FROM ilike_ex 
  WHERE name ILIKE '%j%h%^_do%' ESCAPE '^'
  ORDER BY 1
----
John_down

