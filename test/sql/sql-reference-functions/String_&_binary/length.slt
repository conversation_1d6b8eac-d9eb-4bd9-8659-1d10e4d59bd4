exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE length_function_demo (s VARCHAR);

exclude-from-coverage
statement ok
INSERT INTO length_function_demo VALUES
  (''),
  ('Joyeux Noël'),
  ('Merry Christmas'),
  ('V<PERSON>lé Vianoce'),
  ('<PERSON><PERSON><PERSON><PERSON> Świąt'),
  ('圣诞节快乐'),
  (NULL);

query TT
SELECT s, LENGTH(s) FROM length_function_demo
----
''	0
Joyeux Noël	11
Merry Christmas	15
Veselé Vianoce	14
Wesołych Świąt	14
圣诞节快乐	5
NULL	NULL

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE binary_demo_table (
  v VARCHAR,
  b_hex BINARY,
  b_base64 BINARY,
  b_utf8 BINARY);

exclude-from-coverage
statement ok
INSERT INTO binary_demo_table (v) VALUES ('hello');

exclude-from-coverage
statement ok
UPDATE binary_demo_table SET
  b_hex    = TO_BINARY(HEX_ENCODE(v), 'HEX'),
  b_base64 = TO_BINARY(BASE64_ENCODE(v), 'BASE64'),
  b_utf8   = TO_BINARY(v, 'UTF-8');

query TTTTTTTT
SELECT v, LENGTH(v),
       TO_VARCHAR(b_hex, 'HEX') AS b_hex, LENGTH(b_hex),
       TO_VARCHAR(b_base64, 'BASE64') AS b_base64, LENGTH(b_base64),
       TO_VARCHAR(b_utf8, 'UTF-8') AS b_utf8, LENGTH(b_utf8)
  FROM binary_demo_table
----
hello	5	68656C6C6F	5	aGVsbG8=	5	hello	5

