exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE strings (v VARCHAR(50));

exclude-from-coverage
statement ok
INSERT INTO strings (v) VALUES
  ('San Francisco'),
  ('San Jose'),
  ('Santa Clara'),
  ('Sacramento');

query T
SELECT v
  FROM strings
  WHERE v REGEXP 'San* [fF].*'
  ORDER BY v
----
San Francisco

exclude-from-coverage
statement ok
INSERT INTO strings (v) VALUES
  ('Contains embedded single \\backslash');

query TT
SELECT v, v REGEXP 'San\\b.*' AS matches
  FROM strings
  ORDER BY v
----
Contains embedded single \backslash	FALSE
Sacramento	FALSE
San Francisco	TRUE
San Jose	TRUE
Santa Clara	FALSE

query TT
SELECT v, v REGEXP '.*\\s\\\\.*' AS matches
  FROM strings
  ORDER BY v
----
Contains embedded single \backslash	TRUE
Sacramento	FALSE
San Francisco	FALSE
San Jose	FALSE
Santa Clara	FALSE

query TT
SELECT v, v REGEXP $$.*\s\\.*$$ AS MATCHES
  FROM strings
  ORDER BY v
----
Contains embedded single \backslash	TRUE
Sacramento	FALSE
San Francisco	FALSE
San Jose	FALSE
Santa Clara	FALSE

