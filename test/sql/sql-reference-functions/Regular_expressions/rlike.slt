exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE rlike_ex(city VARCHAR(20));

exclude-from-coverage
statement ok
INSERT INTO rlike_ex VALUES ('Sacramento'), ('San Francisco'), ('San Jose'), (null);

query T
SELECT * FROM rlike_ex WHERE RLIKE(city, 'san.*', 'i')
----
San Francisco
San Jose

query T
SELECT * FROM rlike_ex WHERE NOT RLIKE(city, 'san.*', 'i')
----
Sacramento

query T
SELECT RLIKE('************',
             $$[2-9]\d{2}-\d{3}-\d{4}$$) AS matches_phone_number
----
TRUE

query T
SELECT RLIKE('<EMAIL>',
             $$\w+@[a-zA-Z_]+?\.[a-zA-Z]{2,3}$$) AS matches_email_address
----
TRUE

query T
SELECT RLIKE('************',
             '[2-9]\\d{2}-\\d{3}-\\d{4}') AS matches_phone_number
----
TRUE

query T
SELECT RLIKE('<EMAIL>',
             '\\w+@[a-zA-Z_]+?\\.[a-zA-Z]{2,3}') AS matches_email_address
----
TRUE

query T
SELECT RLIKE('************',
             '[2-9][0-9]{2}-[0-9]{3}-[0-9]{4}') AS matches_phone_number
----
TRUE

query T
SELECT RLIKE('<EMAIL>',
             '[a-zA-Z_]+@[a-zA-Z_]+?\\.[a-zA-Z]{2,3}') AS matches_email_address
----
TRUE

query T
SELECT * FROM rlike_ex WHERE city RLIKE 'San.* [fF].*'
----
San Francisco

