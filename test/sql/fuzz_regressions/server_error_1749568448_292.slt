# Fuzzing regression test
# Generated on: 2025-06-10T18:14:08.908945
# Error type: server_error
# Status code: 500
# Execution time: 0.021s
# Embucket server logs:
# STDOUT: test/logs/embucket_stdout_20250610_181402.log
# STDERR: test/logs/embucket_stderr_20250610_181402.log

# Original error response:
# Service panicked

statement error
WITH cte_data AS (
  SELECT
    t1.status,
    t1.created_at
  FROM transactions AS t1
  WHERE
    t1.id > 0
  ORDER BY
    t1.status
  LIMIT 100
)
SELECT
  *
FROM cte_data
LIMIT 50
