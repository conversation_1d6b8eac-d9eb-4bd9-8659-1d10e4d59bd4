2025-06-10T15:14:02.237567Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-10T15:14:04.178403Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:04.178706Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-10T15:14:04.181326Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:04.208535Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=27 ms status=200
2025-06-10T15:14:04.211570Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:04.308438Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=96 ms status=200
2025-06-10T15:14:04.311628Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:04.507986Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:04.511104Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:04.707875Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:04.710855Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:04.908107Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=197 ms status=200
2025-06-10T15:14:04.911984Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:05.108591Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:05.112803Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:05.309156Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:05.313328Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:05.508159Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=194 ms status=200
2025-06-10T15:14:05.511628Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:05.707402Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=195 ms status=200
2025-06-10T15:14:05.711201Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:05.907980Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:05.910768Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:06.107124Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:06.110898Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:06.307455Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:06.310575Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:06.508126Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=197 ms status=200
2025-06-10T15:14:06.511342Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:06.707855Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:06.710989Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:06.908442Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=197 ms status=200
2025-06-10T15:14:06.912414Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:07.108402Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:07.111966Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:07.307562Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=195 ms status=200
2025-06-10T15:14:07.310631Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:07.509101Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=198 ms status=200
2025-06-10T15:14:07.512746Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:07.707514Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=194 ms status=200
2025-06-10T15:14:07.710342Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:07.908955Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=198 ms status=200
2025-06-10T15:14:07.912947Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:08.108040Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=195 ms status=200
2025-06-10T15:14:08.111987Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:08.307981Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:08.312196Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:08.508747Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=196 ms status=200
2025-06-10T15:14:08.512383Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:08.707153Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=194 ms status=200
2025-06-10T15:14:08.710522Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-10T15:14:08.907690Z DEBUG request{method=POST uri=/ui/queries version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=197 ms status=200
2025-06-10T15:14:08.912760Z  WARN embucketd: SIGTERM received, starting graceful shutdown
2025-06-10T15:14:08.912835Z  WARN embucketd: signal received, starting graceful shutdown
