#!/usr/bin/env python3
"""
Test script to verify the updated query execution tool works with Snowflake connector.
"""

import sys
import os
import json

# Add the current directory to the path so we can import tools
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Test that the updated query execution tool can be imported."""
    try:
        from tools.query_execution_tool import execute_query_against_embucket
        print("✅ Successfully imported execute_query_against_embucket")
        return True
    except ImportError as e:
        print(f"❌ Failed to import query execution tool: {e}")
        return False

def test_function_signature():
    """Test that the function has the expected signature with new parameters."""
    try:
        from tools.query_execution_tool import execute_query_against_embucket
        import inspect
        
        sig = inspect.signature(execute_query_against_embucket)
        params = list(sig.parameters.keys())
        
        expected_params = [
            'sql_query', 'host', 'port', 'protocol', 'account', 
            'user', 'password', 'warehouse', 'database', 'schema'
        ]
        
        if all(param in params for param in expected_params):
            print("✅ Function has correct signature with Snowflake connector parameters")
            return True
        else:
            print(f"❌ Function signature mismatch. Expected: {expected_params}, Got: {params}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking function signature: {e}")
        return False

def test_snowflake_dependency_handling():
    """Test that the function handles missing snowflake-connector-python gracefully."""
    try:
        from tools.query_execution_tool import execute_query_against_embucket, SNOWFLAKE_AVAILABLE
        
        if not SNOWFLAKE_AVAILABLE:
            # Test that it returns appropriate error when snowflake connector is not available
            result_json = execute_query_against_embucket("SELECT 1")
            result = json.loads(result_json)
            
            if not result["success"] and result["error_type"] == "dependency_error":
                print("✅ Correctly handles missing snowflake-connector-python dependency")
                return True
            else:
                print(f"❌ Unexpected result when snowflake connector unavailable: {result}")
                return False
        else:
            print("✅ snowflake-connector-python is available")
            return True
            
    except Exception as e:
        print(f"❌ Error testing dependency handling: {e}")
        return False

def test_database_setup_tool():
    """Test that the database setup tool was also updated."""
    try:
        from tools.database_setup_tool import _execute_sql_query, SNOWFLAKE_AVAILABLE
        print("✅ Successfully imported updated database setup tool")
        
        # Check that it has the SNOWFLAKE_AVAILABLE flag
        if hasattr(sys.modules['tools.database_setup_tool'], 'SNOWFLAKE_AVAILABLE'):
            print("✅ Database setup tool has SNOWFLAKE_AVAILABLE flag")
            return True
        else:
            print("❌ Database setup tool missing SNOWFLAKE_AVAILABLE flag")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import database setup tool: {e}")
        return False

def test_connection_parameters():
    """Test that the function accepts the expected connection parameters."""
    try:
        from tools.query_execution_tool import execute_query_against_embucket
        
        # Test with custom parameters (should not crash even if connection fails)
        result_json = execute_query_against_embucket(
            sql_query="SELECT 1",
            host="test-host",
            port=9999,
            protocol="https",
            account="test-account",
            user="test-user",
            password="test-password",
            warehouse="TEST_WH",
            database="test_db",
            schema="test_schema"
        )
        
        result = json.loads(result_json)
        
        # Should return a valid JSON response (even if connection fails)
        if "success" in result and "execution_time" in result:
            print("✅ Function accepts custom connection parameters and returns valid JSON")
            return True
        else:
            print(f"❌ Unexpected result format: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing connection parameters: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing updated query execution tool...")
    print("=" * 50)
    
    tests = [
        test_import,
        test_function_signature,
        test_snowflake_dependency_handling,
        test_database_setup_tool,
        test_connection_parameters
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The query execution tool has been successfully updated.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
