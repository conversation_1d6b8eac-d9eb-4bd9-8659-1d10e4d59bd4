#!/usr/bin/env python3
"""
Test script to verify that the comprehensive fuzzing tool output size has been reduced.
"""

import sys
import os
import json

# Add the current directory to the path so we can import tools
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_output_size_reduction():
    """Test that the output size has been significantly reduced."""
    try:
        # Mock a typical session result structure to estimate size
        mock_session_results = {
            "session_info": {
                "num_queries": 10,
                "complexity": "complex",
                "host": "localhost",
                "port": 3000,
                "output_dir": "test/sql/fuzz_regressions",
                "start_time": "2024-01-01 12:00:00",
                "end_time": "2024-01-01 12:05:00"
            },
            "server_lifecycle": {
                "build_success": True,
                "start_success": True,
                "database_setup_success": True,
                "stop_success": True,
                "stdout_log_file": "test/logs/stdout.log",
                "stderr_log_file": "test/logs/stderr.log"
            },
            "execution_summary": {
                "queries_executed": 10,
                "successful_queries": 7,
                "expected_errors": 2,
                "actual_bugs": 1,
                "crashes_found": 1,
                "server_errors_found": 0,
                "timeouts_found": 0,
                "reproduction_attempts": 1,
                "successful_reproductions": 1,
                "early_termination": True,
                "early_termination_reason": "Crash detected on query 8"
            },
            "detailed_logs": [],
            "bug_details": [],
            "slt_files_created": ["crash_1234567890_8.slt"],
            "recommendations": [
                "CRITICAL: Crashes detected - investigate immediately",
                "All 1 bugs were reproducible with standalone queries - indicates deterministic issues"
            ]
        }

        # Simulate 10 query logs with truncated data
        for i in range(10):
            query = f"SELECT * FROM users WHERE id = {i} AND name LIKE '%test%' AND created_at > '2024-01-01'"
            if len(query) > 100:
                query = query[:100] + "..."
            
            mock_session_results["detailed_logs"].append({
                "step": f"execute_query_{i+1}",
                "timestamp": "12:00:00",
                "query": query,
                "success": i < 7,
                "error_type": "crash" if i == 7 else None,
                "execution_time": 0.1
            })

        # Simulate 1 bug detail with truncated data
        long_error_message = "This is a very long error message that would normally be much longer and contain detailed stack traces and debugging information" * 5
        if len(long_error_message) > 200:
            long_error_message = long_error_message[:200] + "..."

        long_query = "SELECT u.*, o.*, p.* FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id WHERE u.created_at > '2024-01-01' AND o.status = 'completed'" * 3
        if len(long_query) > 200:
            long_query = long_query[:200] + "..."

        mock_session_results["bug_details"].append({
            "query_number": 8,
            "error_type": "crash",
            "error_message": long_error_message,
            "query": long_query,
            "timestamp": "12:00:00",
            "status_code": None,
            "execution_time": 0.5,
            "reproduction_successful": True,
            "reproduction_details": "Bug REPRODUCED: Same error type 'crash' occurred in isolation..."[:100] + "..."
        })

        # Test compact JSON output (no indentation)
        compact_output = json.dumps(mock_session_results)
        compact_size = len(compact_output)

        # Test what the old format would have been (with indentation and full data)
        old_style_results = mock_session_results.copy()
        
        # Simulate what the old logs would have looked like (with full query results)
        for log in old_style_results["detailed_logs"]:
            if "query" in log:
                # Restore full query
                log["query"] = log["query"].replace("...", " AND more_conditions = 'value' AND another_field IS NOT NULL ORDER BY created_at DESC LIMIT 1000")
                # Add full result object
                log["result"] = {
                    "success": log["success"],
                    "status_code": 200 if log["success"] else 500,
                    "response": '{"columns": ["id", "name", "email", "created_at"], "rows": [[1, "John Doe", "<EMAIL>", "2024-01-01 12:00:00"], [2, "Jane Smith", "<EMAIL>", "2024-01-01 12:01:00"]], "rowCount": 2}' if log["success"] else "Internal server error with detailed stack trace and debugging information",
                    "error_type": log.get("error_type"),
                    "error_message": "Detailed error message with stack trace" if not log["success"] else None,
                    "execution_time": log["execution_time"]
                }

        # Simulate old bug details with full result objects
        for bug in old_style_results["bug_details"]:
            bug["error_message"] = bug["error_message"].replace("...", " with full stack trace and debugging information including file paths, line numbers, and variable states")
            bug["query"] = bug["query"].replace("...", " AND additional_complex_conditions = 'value' ORDER BY complex_expression DESC LIMIT 1000")
            bug["full_result"] = {
                "success": False,
                "status_code": None,
                "response": "Connection refused - server process terminated unexpectedly with detailed error information",
                "error_type": "crash",
                "error_message": "Server process terminated unexpectedly with full stack trace and debugging information",
                "execution_time": 0.5
            }

        old_style_output = json.dumps(old_style_results, indent=2)
        old_style_size = len(old_style_output)

        print(f"✅ Output size comparison:")
        print(f"   Compact output size: {compact_size:,} characters")
        print(f"   Old style output size: {old_style_size:,} characters")
        print(f"   Size reduction: {((old_style_size - compact_size) / old_style_size * 100):.1f}%")

        # Check if we achieved significant reduction
        reduction_percentage = (old_style_size - compact_size) / old_style_size * 100
        if reduction_percentage > 50:
            print(f"✅ Achieved significant size reduction: {reduction_percentage:.1f}%")
            return True
        else:
            print(f"❌ Size reduction not significant enough: {reduction_percentage:.1f}%")
            return False

    except Exception as e:
        print(f"❌ Error testing output size: {e}")
        return False

def test_truncation_logic():
    """Test that our truncation logic works correctly."""
    try:
        # Test query truncation
        long_query = "SELECT * FROM users WHERE name = 'test'" * 10
        truncated = long_query[:100] + "..." if len(long_query) > 100 else long_query
        
        if len(truncated) <= 103:  # 100 chars + "..."
            print("✅ Query truncation works correctly")
        else:
            print(f"❌ Query truncation failed: {len(truncated)} characters")
            return False

        # Test error message truncation
        long_error = "This is a very long error message" * 20
        truncated_error = long_error[:200] + "..." if len(long_error) > 200 else long_error
        
        if len(truncated_error) <= 203:  # 200 chars + "..."
            print("✅ Error message truncation works correctly")
        else:
            print(f"❌ Error message truncation failed: {len(truncated_error)} characters")
            return False

        return True

    except Exception as e:
        print(f"❌ Error testing truncation logic: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing output size reduction...")
    print("=" * 50)
    
    tests = [
        test_output_size_reduction,
        test_truncation_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Output size has been significantly reduced.")
        print("\n📋 Changes made to reduce context length:")
        print("  • Truncated long queries to 100 characters")
        print("  • Truncated error messages to 200 characters")
        print("  • Removed full result objects from logs")
        print("  • Removed JSON indentation (indent=2)")
        print("  • Kept only essential fields in bug details")
        print("  • Truncated reproduction details to 100 characters")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
