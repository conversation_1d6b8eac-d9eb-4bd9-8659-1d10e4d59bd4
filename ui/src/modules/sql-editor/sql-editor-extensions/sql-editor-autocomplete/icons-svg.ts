export const Brackets = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3807 13.3334C13.222 13.3334 13.9047 12.6514 13.9047 11.8094V8.76208L14.6667 8.00008L13.9047 7.23808V4.19075C13.9047 3.34875 13.2227 2.66675 12.3807 2.66675M3.6195 2.66675C2.7775 2.66675 2.0955 3.34875 2.0955 4.19075V7.23808L1.3335 8.00008L2.0955 8.76208V11.8094C2.0955 12.6514 2.7775 13.3334 3.6195 13.3334" stroke="#999999" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Calendar = `<svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13 6.66671H1M9.66667 1.33337V4.00004M4.33333 1.33337V4.00004M4.2 14.6667H9.8C10.9201 14.6667 11.4802 14.6667 11.908 14.4487C12.2843 14.257 12.5903 13.951 12.782 13.5747C13 13.1469 13 12.5868 13 11.4667V5.86671C13 4.7466 13 4.18655 12.782 3.75873C12.5903 3.3824 12.2843 3.07644 11.908 2.88469C11.4802 2.66671 10.9201 2.66671 9.8 2.66671H4.2C3.0799 2.66671 2.51984 2.66671 2.09202 2.88469C1.71569 3.07644 1.40973 3.3824 1.21799 3.75873C1 4.18655 1 4.7466 1 5.86671V11.4667C1 12.5868 1 13.1469 1.21799 13.5747C1.40973 13.951 1.71569 14.257 2.09202 14.4487C2.51984 14.6667 3.0799 14.6667 4.2 14.6667Z" stroke="#999999" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Functions = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_210_1594)">
<path d="M10.3335 10.0002L12.3335 8.00016L10.3335 6.00016M5.66683 6.00016L3.66683 8.00016L5.66683 10.0002M8.66683 4.66683L7.3335 11.3335M14.6668 8.00016C14.6668 11.6821 11.6821 14.6668 8.00016 14.6668C4.31826 14.6668 1.3335 11.6821 1.3335 8.00016C1.3335 4.31826 4.31826 1.3335 8.00016 1.3335C11.6821 1.3335 14.6668 4.31826 14.6668 8.00016Z" stroke="#52CC7A" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_210_1594">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>`;

export const Database = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14 3.3335C14 4.43807 11.3137 5.3335 8 5.3335C4.68629 5.3335 2 4.43807 2 3.3335M14 3.3335C14 2.22893 11.3137 1.3335 8 1.3335C4.68629 1.3335 2 2.22893 2 3.3335M14 3.3335V12.6668C14 13.7735 11.3333 14.6668 8 14.6668C4.66667 14.6668 2 13.7735 2 12.6668V3.3335M14 8.00016C14 9.10683 11.3333 10.0002 8 10.0002C4.66667 10.0002 2 9.10683 2 8.00016" stroke="#F2AADA" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Table = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 6H14M2 10H14M8 2V14M5.2 2H10.8C11.9201 2 12.4802 2 12.908 2.21799C13.2843 2.40973 13.5903 2.71569 13.782 3.09202C14 3.51984 14 4.0799 14 5.2V10.8C14 11.9201 14 12.4802 13.782 12.908C13.5903 13.2843 13.2843 13.5903 12.908 13.782C12.4802 14 11.9201 14 10.8 14H5.2C4.07989 14 3.51984 14 3.09202 13.782C2.71569 13.5903 2.40973 13.2843 2.21799 12.908C2 12.4802 2 11.9201 2 10.8V5.2C2 4.07989 2 3.51984 2.21799 3.09202C2.40973 2.71569 2.71569 2.40973 3.09202 2.21799C3.51984 2 4.0799 2 5.2 2Z" stroke="#91B2F2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Schema = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 3.33333C2 3.68696 2.14048 4.02609 2.39052 4.27614C2.64057 4.52619 2.97971 4.66667 3.33333 4.66667H5.33333M2 2V10.6667C2 11.0203 2.14048 11.3594 2.39052 11.6095C2.64057 11.8595 2.97971 12 3.33333 12H5.33333M13.3333 6.66667C13.5101 6.66667 13.6797 6.59643 13.8047 6.4714C13.9298 6.34638 14 6.17681 14 6V4C14 3.82319 13.9298 3.65362 13.8047 3.5286C13.6797 3.40357 13.5101 3.33333 13.3333 3.33333H11.6667C11.5632 3.33333 11.4611 3.30924 11.3685 3.26295C11.276 3.21667 11.1954 3.14946 11.1333 3.06667L10.5333 2.26667C10.4712 2.18387 10.3907 2.11667 10.2981 2.07038C10.2056 2.0241 10.1035 2 10 2H8.66667C8.48986 2 8.32029 2.07024 8.19526 2.19526C8.07024 2.32029 8 2.48986 8 2.66667V6C8 6.17681 8.07024 6.34638 8.19526 6.4714C8.32029 6.59643 8.48986 6.66667 8.66667 6.66667H13.3333ZM13.3333 14C13.5101 14 13.6797 13.9298 13.8047 13.8047C13.9298 13.6797 14 13.5101 14 13.3333V11.3333C14 11.1565 13.9298 10.987 13.8047 10.8619C13.6797 10.7369 13.5101 10.6667 13.3333 10.6667H11.4C11.278 10.6651 11.1588 10.63 11.0554 10.5654C10.9519 10.5007 10.8682 10.4089 10.8133 10.3L10.5333 9.73334C10.4813 9.61409 10.3955 9.51267 10.2865 9.44159C10.1775 9.37052 10.0501 9.33288 9.92 9.33334H8.66667C8.48986 9.33334 8.32029 9.40358 8.19526 9.5286C8.07024 9.65362 8 9.82319 8 10V13.3333C8 13.5101 8.07024 13.6797 8.19526 13.8047C8.32029 13.9298 8.48986 14 8.66667 14H13.3333Z" stroke="#2EB88A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Keyword = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_395_66223)">
<path d="M10.9999 5.33329C11.184 5.33329 11.3333 5.18405 11.3333 4.99996C11.3333 4.81586 11.184 4.66663 10.9999 4.66663C10.8158 4.66663 10.6666 4.81586 10.6666 4.99996C10.6666 5.18405 10.8158 5.33329 10.9999 5.33329Z" fill="#FAFAFA"/>
<path d="M1.33325 11.9999V13.9999C1.33325 14.3999 1.59992 14.6666 1.99992 14.6666H4.66659V12.6666H6.66659V10.6666H7.99992L8.93325 9.73324C9.85981 10.056 10.8685 10.0548 11.7942 9.72974C12.72 9.40471 13.5081 8.77513 14.0295 7.94399C14.5509 7.11285 14.7749 6.12935 14.6647 5.1544C14.5545 4.17944 14.1167 3.27074 13.4229 2.57695C12.7291 1.88316 11.8204 1.44536 10.8454 1.33516C9.87047 1.22497 8.88698 1.4489 8.05584 1.97033C7.22469 2.49176 6.59511 3.27982 6.27008 4.20558C5.94506 5.13135 5.94382 6.14001 6.26659 7.06657L1.33325 11.9999Z" stroke="#E88C30" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.9999 5.33329C11.184 5.33329 11.3333 5.18405 11.3333 4.99996C11.3333 4.81586 11.184 4.66663 10.9999 4.66663C10.8158 4.66663 10.6666 4.81586 10.6666 4.99996C10.6666 5.18405 10.8158 5.33329 10.9999 5.33329Z" stroke="#E88C30" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_395_66223">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>`;

export const Numberic = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_404_1835)">
<path d="M1.3335 4.97491L14.6668 4.97491M1.3335 10.9924L14.6668 10.9924M5.99023 1.33325L3.67309 14.6666M11.9162 1.33325L9.59901 14.6666" stroke="#999999" stroke-linecap="round"/>
</g>
<defs>
<clipPath id="clip0_404_1835">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>`;

export const String = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.66683 10.8421H7.3335M1.3335 14L4.82556 2L8.66683 14M9.3335 6.93609C9.64722 5.59887 10.2747 4.19483 12.157 4.596C14.0394 4.99716 14.0394 7.27038 14.0394 8.27325V9.61044M14.0394 9.61044V12.6191M14.0394 9.61044C13.7257 9.16471 12.8472 8.27326 11.8433 8.27325C10.5884 8.27325 9.64722 9.61043 9.64722 10.9476C9.64722 12.2848 9.96095 13.622 11.5296 13.9563C12.798 14.2266 13.6211 13.1763 14.0394 12.6191M14.0394 12.6191C14.0394 13.0649 14.1649 13.9563 14.6668 13.9563" stroke="#999999" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Types = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.6665 4.6665C2.6665 4.04525 2.6665 3.73462 2.768 3.48959C2.90332 3.16289 3.16289 2.90332 3.48959 2.768C3.73462 2.6665 4.04525 2.6665 4.6665 2.6665H11.3332C11.9544 2.6665 12.2651 2.6665 12.5101 2.768C12.8368 2.90332 13.0964 3.16289 13.2317 3.48959C13.3332 3.73462 13.3332 4.04525 13.3332 4.6665M5.33317 13.3332H10.6665M6.83317 2.6665V13.3332M9.1665 2.6665V13.3332" stroke="#C2AAF2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Variable = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.2709 14C14.1644 12.1926 14.6668 10.1553 14.6668 8C14.6668 5.84474 14.1644 3.80744 13.2709 2M2.72945 2C1.83592 3.80744 1.3335 5.84474 1.3335 8C1.3335 10.1553 1.83592 12.1926 2.72945 14M11.0326 5.75H10.9728C10.5372 5.75 10.1233 5.94134 9.83986 6.27381L6.25664 10.4762C5.97315 10.8087 5.5593 11 5.1237 11H5.06392M5.81005 5.75H6.73944C7.07256 5.75 7.36531 5.97198 7.45682 6.29396L8.63978 10.456C8.73129 10.778 9.02404 11 9.35716 11H10.2866" stroke="#2E6BE5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;

export const Vector = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.7569 4.92857C14.1302 4.51155 14.3571 3.96085 14.3571 3.35714C14.3571 2.05533 13.3018 1 12 1C10.6982 1 9.64286 2.05533 9.64286 3.35714C9.64286 3.96085 9.86981 4.51155 10.2431 4.92857M13.7569 4.92857C13.3253 5.4108 12.6981 5.71429 12 5.71429C11.3019 5.71429 10.6747 5.4108 10.2431 4.92857M13.7569 4.92857L18.8859 10.4286M18.8859 10.4286C18.5127 10.8456 18.2857 11.3963 18.2857 12C18.2857 12.6037 18.5127 13.1544 18.8859 13.5714M18.8859 10.4286C19.3175 9.94634 19.9447 9.64286 20.6429 9.64286C21.9447 9.64286 23 10.6982 23 12C23 13.3018 21.9447 14.3571 20.6429 14.3571C19.9447 14.3571 19.3175 14.0537 18.8859 13.5714M18.8859 13.5714L13.7569 19.0714M13.7569 19.0714C13.3253 18.5892 12.6981 18.2857 12 18.2857C11.3019 18.2857 10.6747 18.5892 10.2431 19.0714M13.7569 19.0714C14.1302 19.4885 14.3571 20.0391 14.3571 20.6429C14.3571 21.9447 13.3018 23 12 23C10.6982 23 9.64286 21.9447 9.64286 20.6429C9.64286 20.0391 9.86981 19.4885 10.2431 19.0714M10.2431 19.0714L4.92857 13.7569M4.92857 13.7569C5.4108 13.3253 5.71429 12.6981 5.71429 12C5.71429 11.3019 5.4108 10.6747 4.92857 10.2431M4.92857 13.7569C4.51155 14.1302 3.96085 14.3571 3.35714 14.3571C2.05533 14.3571 1 13.3018 1 12C1 10.6982 2.05533 9.64286 3.35714 9.64286C3.96085 9.64286 4.51155 9.86981 4.92857 10.2431M4.92857 10.2431L10.2431 4.92857" stroke="currentColor" stroke-width="inherit" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;
