# Generated by Cargo
# will have compiled files and executables
debug/
target/
object_store/

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb
.DS_Store
# RustRover
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be found at https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
#  and can be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
.idea/

# Added by cargo

/target
venv
__pycache__

.env

slatedb-prefix/
warehouse-prefix/
**/*.parquet
crates/catalog/prefix/**
embucket-warehouse-test/
dummyprefix/
tests/benchmark/*.json
tests/error.log
tests/test_statistics.csv
snowplow

*.manifest
*.sst
*.manifest.json
**.new

.cursor
