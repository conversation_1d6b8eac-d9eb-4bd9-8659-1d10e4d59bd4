version: 2
updates:
  - package-ecosystem: github-actions
    directory: "**/*"
    schedule:
      interval: daily
  - package-ecosystem: cargo
    directory: "**/*"
    schedule:
      interval: daily
    ignore:
      - dependency-name: "*"
        # patch and minor updates don't matter for libraries as consumers of this library build
        # with their own lockfile, rather than the version specified in this library's lockfile
        # remove this ignore rule if your package has binaries to ensure that the binaries are
        # built with the exact set of dependencies and those are up to date.
        update-types:
          - "version-update:semver-patch"
          - "version-update:semver-minor"