[package]
name = "api-ui"
version = "0.1.0"
edition = "2024"
license-file.workspace = true

[features]
client = []

[dependencies]
api-ui-static-assets = { path = "../api-ui-static-assets" }
api-sessions = { path = "../api-sessions" }
core-metastore = { path = "../core-metastore" }
core-utils = { path = "../core-utils" }
core-executor = { path = "../core-executor" }
core-history = { path = "../core-history" }

axum = { workspace = true }
chrono = { workspace = true }
datafusion = { workspace = true }
indexmap = { workspace = true }
jsonwebtoken = { workspace = true }
http = { workspace = true }
mime_guess = "2"
serde = { workspace = true }
serde_json = { workspace = true }
snafu = { workspace = true }
tar = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
tower-http = { workspace = true }
tower-sessions = { workspace = true }
time = { workspace = true }
utoipa = { workspace = true }
uuid = { workspace = true }
validator = { workspace = true }

[dev-dependencies]
reqwest = "0.12.14"

[lints]
workspace = true
