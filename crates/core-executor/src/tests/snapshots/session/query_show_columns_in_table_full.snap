---
source: crates/core-executor/src/tests/query.rs
description: "\"SHOW COLUMNS IN embucket.public.employee_table\""
snapshot_kind: text
---
Ok(
    [
        "+----------------+-------------+---------------+-----------+---------+-------+--------+------------+---------------+---------------+",
        "| table_name     | schema_name | column_name   | data_type | default | null? | kind   | expression | database_name | autoincrement |",
        "+----------------+-------------+---------------+-----------+---------+-------+--------+------------+---------------+---------------+",
        "| employee_table | public      | department_id | Int32     |         | YES   | COLUMN |            | embucket      |               |",
        "| employee_table | public      | employee_id   | Int32     |         | YES   | COLUMN |            | embucket      |               |",
        "| employee_table | public      | first_name    | Utf8      |         | YES   | COLUMN |            | embucket      |               |",
        "| employee_table | public      | last_name     | Utf8      |         | YES   | COLUMN |            | embucket      |               |",
        "+----------------+-------------+---------------+-----------+---------+-------+--------+------------+---------------+---------------+",
    ],
)
