---
source: crates/embucket-functions/src/tests/sql/commands/unpivot.rs
description: "\"WITH sales_data AS (\n  SELECT * FROM monthly_sales WHERE empid < 3\n)\nSELECT *\n  FROM sales_data\n    UNPIVOT (sales FOR month IN (jan, feb, mar, apr))\n  ORDER BY empid;\""
info: "Setup queries: CREATE OR REPLACE TABLE monthly_sales(\n  empid INT,\n  dept TEXT,\n  jan INT,\n  feb INT,\n  mar INT,\n  apr INT); INSERT INTO monthly_sales VALUES\n  (1, 'electronics', 100, 200, 300, 100),\n  (2, 'clothes', 100, 300, 150, 200),\n  (3, 'cars', 200, 400, 100, 50),\n  (4, 'appliances', 100, NULL, 100, 50);"
---
Ok(
    [
        "+-------+-------------+-------+-------+",
        "| empid | dept        | month | sales |",
        "+-------+-------------+-------+-------+",
        "| 1     | electronics | jan   | 100   |",
        "| 1     | electronics | feb   | 200   |",
        "| 1     | electronics | mar   | 300   |",
        "| 1     | electronics | apr   | 100   |",
        "| 2     | clothes     | jan   | 100   |",
        "| 2     | clothes     | feb   | 300   |",
        "| 2     | clothes     | mar   | 150   |",
        "| 2     | clothes     | apr   | 200   |",
        "+-------+-------------+-------+-------+",
    ],
)
