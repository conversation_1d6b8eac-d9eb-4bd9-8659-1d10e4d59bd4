# api-iceberg-rest

Provides the REST API endpoints compliant with the Iceberg REST catalog specification. Handles operations related to Iceberg table and view metadata.

## Purpose

This crate implements the server-side logic for the [Iceberg REST catalog API](https://iceberg.apache.org/spec/#rest-catalog-api). It allows clients that conform to this specification to interact with Iceberg tables and views managed by Embucket.
