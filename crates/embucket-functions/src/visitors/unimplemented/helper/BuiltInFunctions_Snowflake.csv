A,,,,Proposed category,Subcategory(If applied),PureURL
ABS,Returns the absolute value of a numeric expression.,Numeric functions,direct mapping ,numeric,,https://docs.snowflake.com/en/sql-reference/functions/abs
ACOS,"Computes the inverse cosine (arc cosine) of its input; the result is a number in the interval [0, pi].",Numeric functions,direct mapping ,numeric,,https://docs.snowflake.com/en/sql-reference/functions/acos
ACOSH,Computes the inverse (arc) hyperbolic cosine of its input.,Numeric functions,direct mapping ,numeric,,https://docs.snowflake.com/en/sql-reference/functions/acosh
ADD_MONTHS,"Adds or subtracts a specified number of months to a date or timestamp, preserving the end-of-month information.",Date & time functions,need to implement,datetime,,https://docs.snowflake.com/en/sql-reference/functions/add_months
ALERT_HISTORY,This INFORMATION_SCHEMA table function can be used to query the history of alerts within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/alert_history
ALL_USER_NAMES,Returns all user names in the current account.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/all_user_names
ANY_VALUE,Returns some value of the expression from the group.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/any_value
APPLICATION_JSON,Returns a JSON object that specifies the JSON message to use for a notification.,Notification functions,,notification,,https://docs.snowflake.com/en/sql-reference/functions/application_json
APPROX_COUNT_DISTINCT,"Uses HyperLogLog to return an approximation of the distinct cardinality of the input (i.e. HLL(col1, col2, ... ) returns an approximation of COUNT(DISTINCT col1, col2, ... )).","Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_count_distinct
APPROX_PERCENTILE,"Returns an approximated value for the desired percentile (that is, if column c has n numbers, APPROX_PERCENTILE(c, p) returns a number such that approximately n * p of the numbers in c are smaller than the returned number).","Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_percentile
APPROX_PERCENTILE_ACCUMULATE,Returns the internal representation of the t-Digest state (as a JSON object) at the end of aggregation.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_percentile_accumulate
APPROX_PERCENTILE_COMBINE,Combines (merges) percentile input states into a single output state.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_percentile_combine
APPROX_PERCENTILE_ESTIMATE,Returns the desired approximated percentile value for the specified t-Digest state.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_percentile_estimate
APPROX_TOP_K,"Uses Space-Saving to return an approximation of the most frequent values in the input, along with their approximate frequencies.","Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_top_k
APPROX_TOP_K_ACCUMULATE,Returns the Space-Saving summary at the end of aggregation.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_top_k_accumulate
APPROX_TOP_K_COMBINE,Combines (merges) input states into a single output state.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_top_k_combine
APPROX_TOP_K_ESTIMATE,Returns the approximate most frequent values and their estimated frequency for the given Space-Saving state.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approx_top_k_estimate
APPROXIMATE_JACCARD_INDEX,Returns an estimation of the similarity (Jaccard index) of inputs based on their MinHash states.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approximate_jaccard_index
APPROXIMATE_SIMILARITY,Returns an estimation of the similarity (Jaccard index) of inputs based on their MinHash states.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/approximate_similarity
ARRAY_AGG,"Returns the input values, pivoted into an array.","Aggregate functions , Window functions , Semi-structured and structured data functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/array_agg
ARRAY_APPEND,Returns an array containing all elements from the source array as well as the new element.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_append
ARRAY_CAT,Returns a concatenation of two arrays.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_cat
ARRAY_COMPACT,"Returns a compacted array with missing and null values removed, effectively converting sparse arrays into dense arrays.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_compact
ARRAY_CONSTRUCT,"Returns an array constructed from zero, one, or more inputs.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_construct
ARRAY_CONSTRUCT_COMPACT,"Returns an array constructed from zero, one, or more inputs; the constructed array omits any NULL input values.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_construct_compact
ARRAY_CONTAINS,Returns TRUE if the specified value is found in the specified array.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_contains
ARRAY_DISTINCT,Returns a new ARRAY that contains only the distinct elements from the input ARRAY.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_distinct
ARRAY_EXCEPT,Returns a new ARRAY that contains the elements from one input ARRAY that are not in another input ARRAY.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_except
ARRAY_FLATTEN,Flattens an ARRAY of ARRAYs into a single ARRAY.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_flatten
ARRAY_GENERATE_RANGE,"Returns an ARRAY of integer values within a specified range (e.g. [2, 3, 4]).",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_generate_range
ARRAY_INSERT,Returns an array containing all elements from the source array as well as the new element.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_insert
ARRAY_INTERSECTION,Returns an array that contains the matching elements in the two input arrays.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_intersection
ARRAY_MAX,"Given an input ARRAY, returns the element with the highest value that is not a SQL NULL.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_max
ARRAY_MIN,"Given an input ARRAY, returns the element with the lowest value that is not a SQL NULL.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_min
ARRAY_POSITION,Returns the index of the first occurrence of an element in an array.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_position
ARRAY_PREPEND,Returns an array containing the new element as well as all elements from the source array.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_prepend
ARRAY_REMOVE,"Given a source ARRAY, returns an ARRAY with elements of the specified value removed.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_remove
ARRAY_REMOVE_AT,"Given a source ARRAY, returns an ARRAY with the element at the specified position removed.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_remove_at
ARRAY_REVERSE,Returns an array with the elements of the input array in reverse order.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_reverse
ARRAY_SIZE,Returns the size of the input array.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_size
ARRAY_SLICE,Returns an array constructed from a specified subset of elements of the input array.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_slice
ARRAY_SORT,Returns an ARRAY that contains the elements of the input ARRAY sorted in ascending or descending order.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_sort
ARRAY_TO_STRING,Returns an input array converted to a string by casting all values to strings (using TO_VARCHAR) and concatenating them (using the string from the second argument to separate the elements).,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/array_to_string
ARRAY_UNION_AGG,Returns an ARRAY that contains the union of the distinct values from the input ARRAYs in a column.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/array_union_agg
ARRAY_UNIQUE_AGG,Returns an ARRAY that contains all of the distinct values from the specified column.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/array_unique_agg
ARRAYS_OVERLAP,Compares whether two arrays have at least one element in common.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/arrays_overlap
ARRAYS_TO_OBJECT,Returns an OBJECT that contains the keys specified by one input ARRAY and the values specified by another input ARRAY.,Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/arrays_to_object
ARRAYS_ZIP,"Returns an array of objects, each of which contains key-value pairs for an nth element in the input arrays.",Semi-structured and structured data functions,,semi-structured,array,https://docs.snowflake.com/en/sql-reference/functions/arrays_zip
AS_ARRAY,Casts a VARIANT value to an ARRAY value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_array
AS_BINARY,Casts a VARIANT value to a BINARY value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_binary
AS_BOOLEAN,Casts a VARIANT value to a BOOLEAN value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_boolean
"AS_CHAR , AS_VARCHAR",Casts a VARIANT value to a VARCHAR value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_char-varchar
AS_DATE,Casts a VARIANT value to a DATE value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_date
"AS_DECIMAL , AS_NUMBER","Casts a VARIANT value to a fixed-point NUMBER value, with optional precision and scale.",Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_decimal-number
"AS_DOUBLE , AS_REAL",Casts a VARIANT value to a floating-point value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_double-real
AS_INTEGER,Casts a VARIANT value to an INTEGER.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_integer
AS_OBJECT,Casts a VARIANT value to an OBJECT value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_object
AS_TIME,Casts a VARIANT value to a TIME value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_time
AS_TIMESTAMP_*,Casts a VARIANT value to the respective timestamp value.,Semi-structured and structured data functions,,semi-structured,conversion,https://docs.snowflake.com/en/sql-reference/functions/as_timestamp
ASCII,Returns the ASCII code for the first character of a string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/ascii
ASIN,"Computes the inverse sine (arc sine) of its argument; the result is a number in the interval [-pi/2, pi/2].",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/asin
ASINH,Computes the inverse (arc) hyperbolic sine of its argument.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/asinh
ATAN,"Computes the inverse tangent (arc tangent) of its argument; the result is a number in the interval [-pi, pi].",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/atan
ATAN2,Computes the inverse tangent (arc tangent) of the ratio of its two arguments.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/atan2
ATANH,Computes the inverse (arc) hyperbolic tangent of its argument.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/atanh
AUTO_REFRESH_REGISTRATION_HISTORY,This table function can be used to query the history of data files registered in the metadata of specified objects and the credits billed for these operations.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/auto_refresh_registration_history
AUTOMATIC_CLUSTERING_HISTORY,This table function is used for querying the Automatic Clustering history for given tables within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/automatic_clustering_history
AVAILABLE_LISTING_REFRESH_HISTORY,Returns the past 14 days of refresh history for an available listing or a database mounted from a listing using cross-cloud auto-fulfillment.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/available_listing_refresh_history
AVG,Returns the average of non-NULL records.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/avg
B,,,,,,
BASE64_DECODE_BINARY,Decodes a Base64-encoded string to a binary.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/base64_decode_binary
BASE64_DECODE_STRING,Decodes a Base64-encoded string to a string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/base64_decode_string
BASE64_ENCODE,Encodes the input (string or binary) using Base64 encoding.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/base64_encode
[ NOT ] BETWEEN,Returns TRUE when the input expression (numeric or string) is within the specified lower and upper boundary.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/between
BIT_LENGTH,Returns the length of a string or binary value in bits.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/bit_length
BITAND,Returns the bitwise AND of two numeric or binary expressions.,Bitwise expression functions,,bitwise,,https://docs.snowflake.com/en/sql-reference/functions/bitand
BITAND_AGG,Returns the bitwise AND value of all non-NULL numeric records in a group.,"Aggregate functions , Window functions , Bitwise expression functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitand_agg
BITMAP_BIT_POSITION,"Given a numeric value, returns the relative position for the bit that represents that value in a bitmap.",Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitmap_bit_position
BITMAP_BUCKET_NUMBER,"Given a numeric value, returns an identifier (“bucket number”) for the bitmap containing the bit that represents the value..",Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitmap_bucket_number
BITMAP_CONSTRUCT_AGG,Returns a bitmap with bits set for each distinct value in a group.,Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitmap_construct_agg
BITMAP_COUNT,"Given a bitmap that represents the set of distinct values for a column, returns the number of distinct value.",Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitmap_count
BITMAP_OR_AGG,Returns a bitmap containing the results of a binary OR operation on the input bitmaps.,Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitmap_or_agg
BITNOT,Returns the bitwise negation of a numeric or binary expression.,Bitwise expression functions,,bitwise,,https://docs.snowflake.com/en/sql-reference/functions/bitnot
BITOR,Returns the bitwise OR of two numeric or binary expressions.,Bitwise expression functions,,bitwise,,https://docs.snowflake.com/en/sql-reference/functions/bitor
BITOR_AGG,Returns the bitwise OR value of all non-NULL numeric records in a group.,"Aggregate functions , Window functions , Bitwise expression functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitor_agg
BITSHIFTLEFT,Shifts the bits for a numeric or binary expression n positions to the left.,Bitwise expression functions,,bitwise,,https://docs.snowflake.com/en/sql-reference/functions/bitshiftleft
BITSHIFTRIGHT,Shifts the bits for a numeric or binary expression n positions to the right.,Bitwise expression functions,,bitwise,,https://docs.snowflake.com/en/sql-reference/functions/bitshiftright
BITXOR,Returns the bitwise XOR of two numeric or binary expressions.,Bitwise expression functions,,bitwise,,https://docs.snowflake.com/en/sql-reference/functions/bitxor
BITXOR_AGG,Returns the bitwise XOR value of all non-NULL numeric records in a group.,"Aggregate functions , Window functions , Bitwise expression functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/bitxor_agg
BOOLAND,Computes the Boolean AND of two numeric expressions.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/booland
BOOLAND_AGG,Returns TRUE if all non-NULL Boolean records in a group evaluate to TRUE.,"Aggregate functions , Window functions , Conditional expression functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/booland_agg
BOOLNOT,Computes the Boolean NOT of a single numeric expression.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/boolnot
BOOLOR,Computes the Boolean OR of two numeric expressions.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/boolor
BOOLOR_AGG,Returns TRUE if at least one Boolean record in a group evaluates to TRUE.,"Aggregate functions , Window functions , Conditional expression functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/boolor_agg
BOOLXOR,"Computes the Boolean XOR of two numeric expressions (i.e. one of the expressions, but not both expressions, is TRUE).",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/boolxor
BOOLXOR_AGG,Returns TRUE if exactly one Boolean record in the group evaluates to TRUE.,"Aggregate functions , Window functions , Conditional expression functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/boolxor_agg
BUILD_SCOPED_FILE_URL,Generates a scoped Snowflake file URL to a staged file using the stage name and relative file path as inputs.,File functions,,file,,https://docs.snowflake.com/en/sql-reference/functions/build_scoped_file_url
BUILD_STAGE_FILE_URL,Generates a Snowflake file URL to a staged file using the stage name and relative file path as inputs.,File functions,,file,,https://docs.snowflake.com/en/sql-reference/functions/build_stage_file_url
C,,,,,,
CASE,Works like a cascading “if-then-else” statement.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/case
"CAST , ::",Converts a value of one data type into another data type.,Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/cast
CBRT,Returns the cubic root of a numeric expression.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/cbrt
CEIL,"Returns values from input_expr rounded to the nearest equal or larger integer, or to the nearest equal or larger value with the specified number of places after the decimal point.",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/ceil
CHARINDEX,"Searches for the first occurrence of the first argument in the second argument and, if successful, returns the position (1-based) of the first argument in the second argument.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/charindex
CHECK_JSON,Checks the validity of a JSON document.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/check_json
CHECK_XML,Checks the validity of an XML document.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/check_xml
"CHR , CHAR",Converts a Unicode code point (including 7-bit ASCII) into the character that matches the input Unicode.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/chr
CLASSIFY_TEXT (SNOWFLAKE.CORTEX),Classifies free-form text into categories that you provide.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/classify_text-snowflake-cortex
COALESCE,"Returns the first non-NULL expression among its arguments, or NULL if all its arguments are NULL.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/coalesce
COLLATE,"Returns a copy of the original string, but with the specified collation_specification property instead of the original collation_specification property.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/collate
COLLATION,Returns the collation specification of the expression.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/collation
COMPLETE (SNOWFLAKE.CORTEX),"Given a prompt, generates a response (completion) using your choice of supported language model.",String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/complete-snowflake-cortex
COMPLETE_TASK_GRAPHS,Returns the status of a completed graph run.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/complete_task_graphs
COMPRESS,Compresses the input string or binary value with a compression method.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/compress
"CONCAT , ||","Concatenates one or more strings, or concatenates one or more binary values.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/concat
CONCAT_WS,"Concatenates two or more strings, or concatenates two or more binary values.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/concat_ws
CONDITIONAL_CHANGE_EVENT,Returns a window event number for each row within a window partition when the value of the argument expr1 in the current row is different from the value of expr1 in the previous row.,Window functions,,window,,https://docs.snowflake.com/en/sql-reference/functions/conditional_change_event
CONDITIONAL_TRUE_EVENT,Returns a window event number for each row within a window partition based on the result of the boolean argument expr1.,Window functions,,window,,https://docs.snowflake.com/en/sql-reference/functions/conditional_true_event
CONTAINS,Returns true if expr1 contains expr2.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/contains
CONVERT_TIMEZONE,Converts a timestamp to another time zone.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/convert_timezone
COPY_HISTORY,This table function can be used to query Snowflake data loading history along various dimensions within the last 14 days.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/copy_history
CORR,Returns the correlation coefficient for non-null pairs in a group.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/corr
CORTEX_SEARCH_DATA_SCAN,"This table function returns the data indexed by a Cortex Search service, including the columns defined in the source query and the computed vector embeddings for the search column.",Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/cortex_search_data_scan
COS,Computes the cosine of its argument; the argument should be expressed in radians.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/cos
COSH,Computes the hyperbolic cosine of its argument.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/cosh
COT,Computes the cotangent of its argument; the argument should be expressed in radians.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/cot
COUNT,"Returns either the number of non-NULL records for the specified columns, or the total number of records.","Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/count
COUNT_IF,Returns the number of records that satisfy a condition or NULL if no records satisfy the condition.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/count_if
COUNT_TOKENS (SNOWFLAKE.CORTEX),Returns the number of tokens in a prompt for the large language model or the task-specific function specified in the argument.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/count_tokens-snowflake-cortex
COVAR_POP,Returns the population covariance for non-null pairs in a group.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/covar_pop
COVAR_SAMP,Returns the sample covariance for non-null pairs in a group.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/covar_samp
CUME_DIST,Finds the cumulative distribution of a value with regard to other values within the same window partition.,Window functions,,window,,https://docs.snowflake.com/en/sql-reference/functions/cume_dist
CUMULATIVE_PRIVACY_LOSSES,Returns the privacy budgets associated with a specific privacy policy.,Table functions,,account,,https://docs.snowflake.com/en/sql-reference/functions/cumulative_privacy_losses
CURRENT_ACCOUNT,Returns the account locator used by the user’s current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_account
CURRENT_ACCOUNT_NAME,Returns the name of the current account.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_account_name
CURRENT_AVAILABLE_ROLES,Returns a list of all account-level roles granted to the current user.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_available_roles
CURRENT_CLIENT,Returns the version of the client from which the function was called.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_client
CURRENT_DATABASE,"Returns the name of the current database, which varies depending on where you call the function.",Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_database
CURRENT_DATE,Returns the current date of the system.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_date
CURRENT_IP_ADDRESS,Returns the IP address of the client that submitted the request.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_ip_address
CURRENT_ORGANIZATION_NAME,Returns the name of the organization to which the current account belongs.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_organization_name
CURRENT_REGION,Returns the name of the region for the account where the current user is logged in.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_region
CURRENT_ROLE,Returns the name of the primary role in use for the current session when the primary role is an account-level role or NULL if the role in use for the current session is a database role.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_role
CURRENT_ROLE_TYPE,"Returns either ROLE or DATABASE_ROLE based on whether the current active role in the session is an account role or a database role, respectively.",Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_role_type
CURRENT_SCHEMA,"Returns the name of the current schema, which varies depending on where you call the function.",Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_schema
CURRENT_SCHEMAS,Returns active search path schemas.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_schemas
CURRENT_SECONDARY_ROLES,Returns the secondary roles in use for the current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_secondary_roles
CURRENT_SESSION,Returns a unique system identifier for the Snowflake session corresponding to the present connection.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_session
CURRENT_STATEMENT,Returns the SQL text of the statement that is currently executing.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_statement
CURRENT_TASK_GRAPHS,Returns the status of a graph run that is currently scheduled or is executing.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/current_task_graphs
CURRENT_TIME,Returns the current time for the system.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_time
CURRENT_TIMESTAMP,Returns the current timestamp for the system in the local time zone.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_timestamp
CURRENT_TRANSACTION,Returns the transaction id of an open transaction in the current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_transaction
CURRENT_USER,Returns the name of the user currently logged into the system.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_user
CURRENT_VERSION,Returns the current Snowflake version.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_version
CURRENT_WAREHOUSE,Returns the name of the warehouse in use for the current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/current_warehouse
D,,,,,,
DATA_METRIC_FUNCTION_REFERENCES,Returns a row for each object that has the specified data metric function assigned to the object or returns a row for each data metric function assigned to the specified object.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/data_metric_function_references
DATA_QUALITY_MONITORING_RESULTS,"Returns a row for each data metric function assigned to the specified object, which includes the evaluation result and other metadata of the data metric function on the object.","LOCAL , Table functions",,data_quality,,https://docs.snowflake.com/en/sql-reference/functions/data_quality_monitoring_results
DATA_TRANSFER_HISTORY,"This table function can be used to query the history of data transferred from Snowflake tables into a different cloud storage provider’s network (i.e. from Snowflake on AWS, Google Cloud Platform, or Microsoft Azure into the other cloud provider’s network) and/or geographical region within a specified date range.","Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/data_transfer_history
DATABASE_REFRESH_HISTORY,Returns the refresh history for a secondary database.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/database_refresh_history
"DATABASE_REFRESH_PROGRESS , DATABASE_REFRESH_PROGRESS_BY_JOB",The DATABASE_REFRESH_PROGRESS family of functions can be used to query the status of a database refresh along various dimensions.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/database_refresh_progress
DATABASE_REPLICATION_USAGE_HISTORY,This table function can be used to query the replication history for a specified database within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/database_replication_usage_history
DATABASE_STORAGE_USAGE_HISTORY,"This table function can be used to query the average daily storage usage, in bytes, for a single database (or all the databases in your account) within a specified date range.","Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/database_storage_usage_history
DATE_FROM_PARTS,"Creates a date from individual numeric components that represent the year, month, and day of the month.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/date_from_parts
DATE_PART,"Extracts the specified date or time part from a date, time, or timestamp.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/date_part
DATE_TRUNC,"Truncates a DATE, TIME, or TIMESTAMP value to the specified precision.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/date_trunc
DATEADD,"Adds the specified value for the specified date or time part to a date, time, or timestamp.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/dateadd
DATEDIFF,"Calculates the difference between two date, time, or timestamp expressions based on the date or time part requested.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/datediff
DAYNAME,Extracts the three-letter day-of-week name from the specified date or timestamp.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/dayname
DECODE,Compares the select expression to each search expression in order.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/decode
DECOMPRESS_BINARY,Decompresses the compressed BINARY input parameter.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/decompress_binary
DECOMPRESS_STRING,Decompresses the compressed BINARY input parameter to a string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/decompress_string
DECRYPT,Decrypts a BINARY value using a VARCHAR passphrase.,Encryption functions,,encryption,,https://docs.snowflake.com/en/sql-reference/functions/decrypt
DECRYPT_RAW,Decrypts a BINARY value using a BINARY key.,Encryption functions,,encryption,,https://docs.snowflake.com/en/sql-reference/functions/decrypt_raw
DEGREES,Converts radians to degrees.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/degrees
DENSE_RANK,"Returns the rank of a value within a group of values, without gaps in the ranks.",Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/dense_rank
DIV0,"Performs division like the division operator (/), but returns 0 when the divisor is 0 (rather than reporting an error).",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/div0
DIV0NULL,"Performs division like the division operator (/), but returns 0 when the divisor is 0 or NULL (rather than reporting an error or returning NULL).",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/div0null
AVG (system data metric function),Returns the average value for the specified column in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_avg
BLANK_COUNT (system data metric function),Returns the count of column values that are blank for the specified column in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_blank_count
BLANK_PERCENT (system data metric function),Returns the percentage of column values that are blank for the specified column in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_blank_percent
DATA_METRIC_SCHEDULED_TIME (system data metric function),Returns the timestamp for when a DMF is scheduled to run or the current timestamp if the function is called manually.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_data_metric_schedule_time
DUPLICATE_COUNT (system data metric function),"Returns the count of column values that have duplicates, including NULL values.",Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_duplicate_count
FRESHNESS (system data metric function),Returns the difference in seconds between the maximum value of a timestamp column and the scheduled time when the data metric function runs.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_freshness
MAX (system data metric function),Returns the maximum value for the specified column in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_max
MIN (system data metric function),Returns the minimum value for the specified column in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_min
NULL_COUNT (system data metric function),Returns the total number of NULL values for the specified columns in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_null_count
NULL_PERCENT (system data metric function),Returns the percentage of columns values that are NULL for the specified column in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_null_percent
ROW_COUNT (system data metric function),Returns the total number of rows for the specified table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_row_count
STDDEV (system data metric function),Returns the standard deviation value for the specified column in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_stddev
UNIQUE_COUNT (system data metric function),Returns the total number of unique non-NULL values for the specified columns in a table.,Data metric functions,,data_metric,,https://docs.snowflake.com/en/sql-reference/functions/dmf_unique_count
DP_INTERVAL_HIGH,"Returns the upper bound of the noise interval, which is used by differential privacy to help analysts determine how much noise has been introduced into query results.",Differential privacy functions,,differential_privacy,,https://docs.snowflake.com/en/sql-reference/functions/dp_interval_high
DP_INTERVAL_LOW,"Returns the lower bound of the noise interval, which is used by differential privacy to help analysts determine how much noise has been introduced into query results.",Differential privacy functions,,differential_privacy,,https://docs.snowflake.com/en/sql-reference/functions/dp_interval_low
DYNAMIC_TABLE_GRAPH_HISTORY,This table function returns information on all dynamic tables in the current account.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/dynamic_table_graph_history
DYNAMIC_TABLE_REFRESH_HISTORY,This table function returns information about each refresh (completed and running) of dynamic tables.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/dynamic_table_refresh_history
DYNAMIC_TABLES,"This table function returns metadata about dynamic tables, including aggregate lag metrics and the status of the most recent refreshes, within 7 days of the current time.","Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/dynamic_tables
E,,,,,,
EDITDISTANCE,Computes the Levenshtein distance between two input strings.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/editdistance
EMAIL_INTEGRATION_CONFIG,"Returns a JSON object that specifies the email notification integration, recipients, and subject line to use for an email notification.",Notification functions,,notification,,https://docs.snowflake.com/en/sql-reference/functions/email_integration_config
EMBED_TEXT_768 (SNOWFLAKE.CORTEX),Creates a vector embedding of 768 dimensions from English-language text.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/embed_text-snowflake-cortex
EMBED_TEXT_1024 (SNOWFLAKE.CORTEX),Creates a vector embedding of 1024 dimensions from text.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/embed_text_1024-snowflake-cortex
ENCRYPT,Encrypts a VARCHAR or BINARY value using a VARCHAR passphrase.,Encryption functions,,encryption,,https://docs.snowflake.com/en/sql-reference/functions/encrypt
ENCRYPT_RAW,Encrypts a BINARY value using a BINARY key.,Encryption functions,,encryption,,https://docs.snowflake.com/en/sql-reference/functions/encrypt_raw
ENDSWITH,Returns TRUE if the first expression ends with the second expression.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/endswith
EQUAL_NULL,Compares whether two expressions are equal.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/equal_null
ESTIMATE_REMAINING_DP_AGGREGATES,Returns the estimated number of aggregation functions that can be run before the limit of a privacy budget is reached.,"Differential privacy functions , Table functions",,account,,https://docs.snowflake.com/en/sql-reference/functions/estimate_remaining_dp_aggregates
EXP,Computes Euler’s number e raised to a floating-point value.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/exp
EXPLAIN_JSON,This function converts an EXPLAIN plan from JSON to a table.,System functions,,semi-structured,json,https://docs.snowflake.com/en/sql-reference/functions/explain_json
EXTERNAL_FUNCTIONS_HISTORY,This table function retrieves the history of external functions called by Snowflake for your entire Snowflake account.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/external_functions_history
EXTERNAL_TABLE_FILES,This table function can be used to query information about the staged data files included in the metadata for a specified external table.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/external_table_files
EXTERNAL_TABLE_FILE_REGISTRATION_HISTORY,This table function can be used to query information about the metadata history for an external table.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/external_table_registration_history
EXTRACT,"Extracts the specified date or time part from a date, time, or timestamp.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/extract
EXTRACT_ANSWER (SNOWFLAKE.CORTEX),Extracts an answer to a given question from a text document.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/extract_answer-snowflake-cortex
EXTRACT_SEMANTIC_CATEGORIES,Returns a set of categories (semantic and privacy) for each supported column in the specified table or view.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/extract_semantic_categories
F,,,,,,
FACTORIAL,Computes the factorial of its input.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/factorial
FILTER,Filters an array based on the logic in a lambda expression.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/filter
FINETUNE ('CANCEL') (SNOWFLAKE.CORTEX),Cancels the specified fine-tuning job from the current schema.,,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/finetune-cancel
FINETUNE ('CREATE') (SNOWFLAKE.CORTEX),Creates a fine-tuning job.,,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/finetune-create
FINETUNE ('DESCRIBE') (SNOWFLAKE.CORTEX),Describes the properties of a fine-tuning job.,,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/finetune-describe
FINETUNE ('SHOW') (SNOWFLAKE.CORTEX),Lists the fine-tuning jobs for which you have access privileges.,,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/finetune-show
FINETUNE (SNOWFLAKE.CORTEX),This function lets you create and manage large language models customized for your specific task.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/finetune-snowflake-cortex
FIRST_VALUE,Returns the first value within an ordered group of values.,Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/first_value
FLATTEN,Flattens (explodes) compound values into multiple rows.,"Table functions , Semi-structured and structured data functions",,table,,https://docs.snowflake.com/en/sql-reference/functions/flatten
FLOOR,"Returns values from input_expr rounded to the nearest equal or smaller integer, or to the nearest equal or smaller value with the specified number of places after the decimal point.",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/floor
G,,,,,,
GENERATE_COLUMN_DESCRIPTION,Generates a list of columns from a set of staged files that contain semi-structured data using the INFER_SCHEMA function output.,Metadata functions,,metadata,,https://docs.snowflake.com/en/sql-reference/functions/generate_column_description
GENERATOR,"Creates rows of data based either on a specified number of rows, a specified generation period (in seconds), or both.",Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/generator
GET,Extracts a value from an ARRAY or an OBJECT (or a VARIANT that contains an ARRAY or OBJECT).,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/get
GET_ABSOLUTE_PATH,Retrieves the absolute path of a staged file using the stage name and path of the file relative to its location in the stage as inputs.,File functions,,file,,https://docs.snowflake.com/en/sql-reference/functions/get_absolute_path
GET_ANACONDA_PACKAGES_REPODATA,Returns a list of third-party packages that are available from Anaconda.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/get_anaconda_packages_repodata
GET_CONDITION_QUERY_UUID,Returns the query ID for the SQL statement executed for the condition of an alert.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/get_condition_query_uuid
GET_DDL,Returns a DDL statement that can be used to recreate the specified object.,Metadata functions,,metadata,,https://docs.snowflake.com/en/sql-reference/functions/get_ddl
GET_IGNORE_CASE,Extracts a field value from an object; returns NULL if either of the arguments is NULL.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/get_ignore_case
GET_LINEAGE (SNOWFLAKE.CORE),"Given a Snowflake object, returns data lineage information upstream or downstream from that object.",Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/get_lineage-snowflake-core
GET_OBJECT_REFERENCES,Returns a list of objects that a specified object references.,Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/get_object_references
"GET_PATH , :",Extracts a value from semi-structured data using a path name.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/get_path
GET_PRESIGNED_URL,Generates a pre-signed URL to a file on a stage using the stage name and relative file path as inputs.,File functions,,file,,https://docs.snowflake.com/en/sql-reference/functions/get_presigned_url
GET_PYTHON_PROFILER_OUTPUT (SNOWFLAKE.CORE),Returns output containing a report generated by the Python code profiler.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/get_python_profiler_output
GET_QUERY_OPERATOR_STATS,Returns statistics about individual query operators within a query that has completed.,"System functions , Table functions",,system,,https://docs.snowflake.com/en/sql-reference/functions/get_query_operator_stats
GET_RELATIVE_PATH,Extracts the path of a staged file relative to its location in the stage using the stage name and absolute file path in cloud storage as inputs.,File functions,,file,,https://docs.snowflake.com/en/sql-reference/functions/get_relative_path
GET_STAGE_LOCATION,Retrieves the URL for an external or internal named stage using the stage name as the input.,File functions,,file,,https://docs.snowflake.com/en/sql-reference/functions/get_stage_location
GETBIT,"Given an INTEGER value, returns the value of a bit at a specified position.",Bitwise expression functions,,bitwise,,https://docs.snowflake.com/en/sql-reference/functions/getbit
GETDATE,Returns the current timestamp for the system in the local time zone.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/getdate
GETVARIABLE,Returns the value associated with a SQL variable name.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/getvariable
GREATEST,Returns the largest value from a list of expressions.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/greatest
GREATEST_IGNORE_NULLS,Returns the largest non-NULL value from a list of expressions.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/greatest_ignore_nulls
GROUPING,Describes which of a list of expressions are grouped in a row produced by a GROUP BY query.,Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/grouping
GROUPING_ID,Describes which of a list of expressions are grouped in a row produced by a GROUP BY query.,Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/grouping_id
H,,,,,,
H3_CELL_TO_BOUNDARY,Returns the GEOGRAPHY object representing the boundary of an H3 cell.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_boundary
H3_CELL_TO_CHILDREN,Returns an array of the INTEGER IDs of the children of an H3 cell for a given resolution.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_children
H3_CELL_TO_CHILDREN_STRING,Returns an array of the VARCHAR values containing the hexadecimal IDs of the children of an H3 cell for a given resolution.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_children_string
H3_CELL_TO_PARENT,Returns the ID of the parent of an H3 cell for a given resolution.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_parent
H3_CELL_TO_POINT,Returns the GEOGRAPHY object representing the Point that is the centroid of an H3 cell.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_point
H3_COMPACT_CELLS,"Returns an array of VARIANT values that contain the INTEGER IDs of fewer, larger H3 cells that cover the same area as the H3 cells in the input.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_compact_cells
H3_COMPACT_CELLS_STRINGS,"Returns an array of VARIANT values that contain the VARCHAR hexadecimal IDs of fewer, larger H3 cells that cover the same area as the H3 cells in the input.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_compact_cells_strings
H3_COVERAGE,Returns an array of IDs (as INTEGER values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_coverage
H3_COVERAGE_STRINGS,Returns an array of hexadecimal IDs (as VARCHAR values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_coverage_strings
H3_GET_RESOLUTION,Returns the resolution of an H3 cell.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_get_resolution
H3_GRID_DISK,Returns an array of the IDs of the H3 cells that are within the k-distance from the specified cell.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_grid_disk
H3_GRID_DISTANCE,Returns the distance between two H3 cells specified by their IDs.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_grid_distance
H3_GRID_PATH,Returns an array of the IDs of the H3 cells that represent the line between two cells.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_grid_path
H3_INT_TO_STRING,Converts the INTEGER value of an H3 cell ID to hexadecimal format.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_int_to_string
H3_IS_PENTAGON,Returns TRUE if the boundary of an H3 cell represents a pentagon.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_is_pentagon
H3_IS_VALID_CELL,Returns TRUE if the input represents a valid H3 cell.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_is_valid_cell
H3_LATLNG_TO_CELL,"Returns the INTEGER value of the H3 cell ID for a given latitude, longitude, and resolution.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_latlng_to_cell
H3_LATLNG_TO_CELL_STRING,"Returns the H3 cell ID in hexadecimal format (as a VARCHAR value) for a given latitude, longitude, and resolution.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_latlng_to_cell_string
H3_POINT_TO_CELL,Returns the INTEGER value of an H3 cell ID for a Point (specified by a GEOGRAPHY object) at a given resolution.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_point_to_cell
H3_POINT_TO_CELL_STRING,Returns the hexadecimal value of an H3 cell ID for a Point (specified by a GEOGRAPHY object) at a given resolution.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_point_to_cell_string
H3_POLYGON_TO_CELLS,Returns an array of INTEGER values of the IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_polygon_to_cells
H3_POLYGON_TO_CELLS_STRINGS,Returns an array of VARCHAR values of the hexadecimal IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_polygon_to_cells_strings
H3_STRING_TO_INT,Converts an H3 cell ID in hexadecimal format to an INTEGER value.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_string_to_int
H3_TRY_COVERAGE,A special version of H3_COVERAGE that returns NULL if an error occurs when it attempts to return an array of IDs (as INTEGER values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_try_coverage
H3_TRY_COVERAGE_STRINGS,A special version of H3_COVERAGE_STRINGS that returns NULL if an error occurs when it attempts to return an array of hexadecimal IDs (as VARCHAR values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_try_coverage_strings
H3_TRY_GRID_DISTANCE,A special version of H3_GRID_DISTANCE that returns NULL if an error occurs when it attempts to return the distance between two H3 cells.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_try_grid_distance
H3_TRY_GRID_PATH,A special version of H3_GRID_PATH that returns NULL if an error occurs when it attempts to return an array of VARIANT values that contain the IDs of the H3 cells that represent the line between two cells.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_try_grid_path
H3_TRY_POLYGON_TO_CELLS,A special version of H3_POLYGON_TO_CELLS that returns NULL if an error occurs when it attempts to return an array of INTEGER values of the IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_try_polygon_to_cells
H3_TRY_POLYGON_TO_CELLS_STRINGS,A special version of H3_POLYGON_TO_CELLS_STRINGS that returns NULL if an error occurs when it attempts to return an array of VARCHAR values of the hexadecimal IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_try_polygon_to_cells_strings
H3_UNCOMPACT_CELLS,Returns an array of VARIANT values that contain the INTEGER IDs of H3 cells at the specified resolution that cover the same area as the H3 cells in the input.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_uncompact_cells
H3_UNCOMPACT_CELLS_STRINGS,Returns an array of VARIANT values that contain the VARCHAR hexadecimal IDs of H3 cells at the specified resolution that cover the same area as the H3 cells in the input.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/h3_uncompact_cells_strings
HASH,Returns a signed 64-bit hash value.,Hash functions,,hash,,https://docs.snowflake.com/en/sql-reference/functions/hash
HASH_AGG,Returns an aggregate signed 64-bit hash value over the (unordered) set of input rows.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/hash_agg
HAVERSINE,"Calculates the great-circle distance in kilometers between two points on the Earth’s surface, using the Haversine formula.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/haversine
HEX_DECODE_BINARY,Decodes a hex-encoded string to a binary.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/hex_decode_binary
HEX_DECODE_STRING,Decodes a hex-encoded string to a string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/hex_decode_string
HEX_ENCODE,Encodes the input using hexadecimal (also ‘hex’ or ‘base16’) encoding.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/hex_encode
HLL,"Uses HyperLogLog to return an approximation of the distinct cardinality of the input (i.e. HLL(col1, col2, ... ) returns an approximation of COUNT(DISTINCT col1, col2, ... )).","Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/hll
HLL_ACCUMULATE,Returns the HyperLogLog state at the end of aggregation.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/hll_accumulate
HLL_COMBINE,Combines (merges) input states into a single output state.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/hll_combine
HLL_ESTIMATE,Returns the cardinality estimate for the given HyperLogLog state.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/hll_estimate
HLL_EXPORT,Converts input in BINARY format to OBJECT format.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/hll_export
HLL_IMPORT,Converts input in OBJECT format to BINARY format.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/hll_import
HOUR / MINUTE / SECOND,Extracts the corresponding time part from a time or timestamp value.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/hour-minute-second
I,,,,,,
ICEBERG_TABLE_FILES,Returns information about the data files registered to an externally managed,iceberg-tm,,iceberg,,https://docs.snowflake.com/en/sql-reference/functions/iceberg_table_files
ICEBERG_TABLE_SNAPSHOT_REFRESH_HISTORY,Returns metadata and snapshot information about the most recent refresh history for a specified externally managed,iceberg-tm,,iceberg,,https://docs.snowflake.com/en/sql-reference/functions/iceberg_table_snapshot_refresh_history
IFF,Returns one of two values depending on whether a Boolean expression evaluates to true or false.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/iff
IFNULL,"If expr1 is NULL, returns expr2, otherwise returns expr1.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/ifnull
[ NOT ] ILIKE,Performs a case-insensitive comparison to determine whether a string matches or does not match a specified pattern.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/ilike
ILIKE ANY,Performs a case-insensitive comparison to match a string against any of one or more specified patterns.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/ilike_any
[ NOT ] IN,Tests whether its argument is or is not one of the members of an explicit list or the result of a subquery.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/in
INFER_SCHEMA,Automatically detects the file metadata schema in a set of staged data files that contain semi-structured data and retrieves the column definitions.,Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/infer_schema
INITCAP,Returns the input string with the first letter of each word in uppercase and the subsequent letters in lowercase.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/initcap
INSERT,"Replaces a substring of the specified length, starting at the specified position, with a new string or binary value.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/insert
INTEGRATION,Returns a JSON object that specifies the notification integration to use to send a message.,Notification functions,,notification,,https://docs.snowflake.com/en/sql-reference/functions/integration
INVOKER_ROLE,Returns the name of the account-level role of the object executing the query or NULL if the name of the role is a database role.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/invoker_role
INVOKER_SHARE,"Returns the name of the share that directly accessed the table or view where the INVOKER_SHARE function is invoked, otherwise the function returns NULL.",Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/invoker_share
IS [ NOT ] DISTINCT FROM,Compares whether two expressions are equal (or not equal).,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/is-distinct-from
IS [ NOT ] NULL,Determines whether an expression is NULL or is not NULL.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/is-null
IS_APPLICATION_ROLE_IN_SESSION,Verifies whether the application role is activated in the consumer’s current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/is_application_role_in_session
IS_ARRAY,Returns TRUE if its VARIANT argument contains an ARRAY value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_array
IS_BINARY,Returns TRUE if its VARIANT argument contains a binary string value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_binary
IS_BOOLEAN,Returns TRUE if its VARIANT argument contains a BOOLEAN value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_boolean
"IS_CHAR , IS_VARCHAR",Returns TRUE if its VARIANT argument contains a string value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_char-varchar
IS_DATABASE_ROLE_IN_SESSION,Verifies whether the database role is in the user’s active primary or secondary role hierarchy for the current session or if the specified column contains a database role that is in the user’s active primary or secondary role hierarchy for the current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/is_database_role_in_session
"IS_DATE , IS_DATE_VALUE",Returns TRUE if its VARIANT argument contains a DATE value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_date-value
IS_DECIMAL,Returns TRUE if its VARIANT argument contains a fixed-point number or integer value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_decimal
"IS_DOUBLE , IS_REAL","Returns TRUE if its VARIANT argument contains a floating-point number, fixed-point number, or integer value.",Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_double-real
IS_GRANTED_TO_INVOKER_ROLE,Returns TRUE if the role returned by the INVOKER_ROLE function inherits the privileges of the specified role in the argument based on the context in which the function is called.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/is_granted_to_invoker_role
IS_INSTANCE_ROLE_IN_SESSION,Verifies whether the user’s active primary or secondary role hierarchy for the session inherits the specified instance role.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/is_instance_role_in_session
IS_INTEGER,Returns TRUE if its VARIANT argument contains an integer value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_integer
IS_NULL_VALUE,Returns TRUE if its VARIANT argument is a JSON null value.,"Conditional expression functions , Semi-structured and structured data functions",,semi-structured,json,https://docs.snowflake.com/en/sql-reference/functions/is_null_value
IS_OBJECT,Returns TRUE if its VARIANT argument contains an OBJECT value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_object
IS_ROLE_IN_SESSION,Verifies whether the account role is in the user’s active primary or secondary role hierarchy for the session or if the specified column contains a role that is in the user’s active primary or secondary role hierarchy for the session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/is_role_in_session
IS_TIME,Verifies whether a VARIANT argument contains a TIME value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_time
IS_TIMESTAMP_*,Verifies whether a VARIANT argument contains the respective timestamp value.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/is_timestamp
J,,,,,,
JAROWINKLER_SIMILARITY,Computes the Jaro-Winkler similarity between two input strings.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/jarowinkler_similarity
JSON_EXTRACT_PATH_TEXT,Parses the first argument as a JSON string and returns the value of the element pointed to by the path in the second argument.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/json_extract_path_text
K,,,,,,
KURTOSIS,Returns the population excess kurtosis of non-NULL records.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/kurtosis
L,,,,,,
LAG,Accesses data in a previous row in the same result set without having to join the table to itself.,Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/lag
LAST_DAY,Returns the last day of the specified date part for a date or timestamp.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/last_day
LAST_QUERY_ID,Returns the ID of a specified query in the current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/last_query_id
LAST_SUCCESSFUL_SCHEDULED_TIME,"Returns the timestamp representing the scheduled time for the most recent successful evaluation of the alert condition, where no errors occurred when executing the action.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/last_successful_scheduled_time
LAST_TRANSACTION,Returns the transaction ID of the last transaction that was either committed or rolled back in the current session.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/last_transaction
LAST_VALUE,Returns the last value within an ordered group of values.,Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/last_value
LEAD,Accesses data in a subsequent row in the same result set without having to join the table to itself.,Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/lead
LEAST,Returns the smallest value from a list of expressions.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/least
LEAST_IGNORE_NULLS,Returns the smallest non-NULL value from a list of expressions.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/least_ignore_nulls
LEFT,Returns a leftmost substring of its input.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/left
"LENGTH, LEN",Returns the length of an input string or binary value.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/length
[ NOT ] LIKE,Performs a case-sensitive comparison to determine whether a string matches or does not match a specified pattern.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/like
LIKE ALL,Performs a case-sensitive comparison to match a string against all of one or more specified patterns.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/like_all
LIKE ANY,Performs a case-sensitive comparison to match a string against any of one or more specified patterns.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/like_any
LISTAGG,"Returns the concatenated input values, separated by the delimiter string.","Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/listagg
LISTING_REFRESH_HISTORY,Returns the past 14 days of refresh history for a cross-cloud auto-fulfillment listing.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/listing_refresh_history
LN,Returns the natural logarithm of a numeric expression.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/ln
LOCALTIME,Returns the current time for the system.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/localtime
LOCALTIMESTAMP,Returns the current timestamp for the system in the local time zone.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/localtimestamp
LOG,Returns the logarithm of a numeric expression.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/log
"LOGIN_HISTORY , LOGIN_HISTORY_BY_USER",The LOGIN_HISTORY family of table functions can be used to query login attempts by Snowflake users along various dimensions.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/login_history
LOWER,Returns the input string with all characters converted to lowercase.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/lower
LPAD,"Left-pads a string with characters from another string, or left-pads a binary value with bytes from another binary value.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/lpad
LTRIM,"Removes leading characters, including whitespace, from a string.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/ltrim
M,,,,,,
MAP_CAT,Returns the concatenatation of two MAPs.,Semi-structured and structured data functions,,semi-structured,map,https://docs.snowflake.com/en/sql-reference/functions/map_cat
MAP_CONTAINS_KEY,Determines whether the specified MAP contains the specified key.,Semi-structured and structured data functions,,semi-structured,map,https://docs.snowflake.com/en/sql-reference/functions/map_contains_key
MAP_DELETE,Returns a MAP based on an existing MAP with one or more keys removed..,Semi-structured and structured data functions,,semi-structured,map,https://docs.snowflake.com/en/sql-reference/functions/map_delete
MAP_INSERT,Returns a new MAP consisting of the input MAP with a new key-value pair inserted (an existing key updated with a new value).,Semi-structured and structured data functions,,semi-structured,map,https://docs.snowflake.com/en/sql-reference/functions/map_insert
MAP_KEYS,Returns the keys in a MAP.,Semi-structured and structured data functions,,semi-structured,map,https://docs.snowflake.com/en/sql-reference/functions/map_keys
MAP_PICK,Returns a new MAP containing the specified key-value pairs from an existing MAP.,Semi-structured and structured data functions,,semi-structured,map,https://docs.snowflake.com/en/sql-reference/functions/map_pick
MAP_SIZE,Returns the size of a MAP.,Semi-structured and structured data functions,,semi-structured,map,https://docs.snowflake.com/en/sql-reference/functions/map_size
MATERIALIZED_VIEW_REFRESH_HISTORY,This table function is used for querying the materialized views refresh history for a specified materialized view within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/materialized_view_refresh_history
MAX,Returns the maximum value for the records within expr.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/max
MAX_BY,Finds the row(s) containing the maximum value for a column and returns the value of another column in that row.,Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/max_by
"MD5 , MD5_HEX",Returns a 32-character hex-encoded string containing the 128-bit MD5 message digest.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/md5
MD5_BINARY,Returns a 16-byte BINARY value containing the 128-bit MD5 message digest.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/md5_binary
MD5_NUMBER — Obsoleted,Returns the 128-bit MD5 message digest interpreted as a signed 128-bit big endian number.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/md5_number
MD5_NUMBER_LOWER64,"Calculates the 128-bit MD5 message digest, interprets it as a signed 128-bit big endian number, and returns the lower 64 bits of the number as an unsigned integer.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/md5_number_lower64
MD5_NUMBER_UPPER64,"Calculates the 128-bit MD5 message digest, interprets it as a signed 128-bit big endian number, and returns the upper 64 bits of the number as an unsigned integer.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/md5_number_upper64
MEDIAN,Determines the median of a set of values.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/median
MIN,Returns the minimum value for the records within expr.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/min
MIN_BY,Finds the row(s) containing the minimum value for a column and returns the value of another column in that row.,Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/min_by
MINHASH,Returns a MinHash state containing an array of size k constructed by applying k number of different hash functions to the input rows and keeping the minimum of each hash function.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/minhash
MINHASH_COMBINE,Combines input MinHash states into a single MinHash output state.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/minhash_combine
MOD,Returns the remainder of input expr1 divided by input expr2.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/mod
MODE,Returns the most frequent value for the values within expr1.,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/mode
MONTHNAME,Extracts the three-letter month name from the specified date or timestamp.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/monthname
MONTHS_BETWEEN,Returns the number of months between two DATE or TIMESTAMP values.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/months_between
N,,,,,,
NETWORK_RULE_REFERENCES,Returns a row for each object with which the specified network rule is associated or returns a row for each network rule associated with the specified container.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/network_rule_references
NEXT_DAY,Returns the date of the first specified DOW (day of week) that occurs after the input date.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/next_day
NORMAL,Generates a normally-distributed pseudo-random floating point number with specified mean and stddev (standard deviation).,Data generation functions,,generation,,https://docs.snowflake.com/en/sql-reference/functions/normal
NOTIFICATION_HISTORY,This table function can be used to query the history of notifications sent through Snowflake.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/notification_history
NTH_VALUE,Returns the nth value (up to 1000) within an ordered group of values.,Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/nth_value
NTILE,Divides an ordered data set equally into the number of buckets specified by constant_value.,Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/ntile
NULLIF,"Returns NULL if expr1 is equal to expr2, otherwise returns expr1.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/nullif
NULLIFZERO,"Returns NULL if the argument evaluates to 0; otherwise, returns the argument.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/nullifzero
NVL,"If expr1 is NULL, returns expr2, otherwise returns expr1.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/nvl
NVL2,Returns values depending on whether the first input is NULL.,Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/nvl2
O,,,,,,
OBJECT_AGG,Returns one OBJECT per group.,"Aggregate functions , Window functions , Semi-structured and structured data functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/object_agg
OBJECT_CONSTRUCT,Returns an OBJECT constructed from the arguments.,Semi-structured and structured data functions,,semi-structured,object,https://docs.snowflake.com/en/sql-reference/functions/object_construct
OBJECT_CONSTRUCT_KEEP_NULL,Returns an OBJECT constructed from the arguments that retains key-values pairs with NULL values.,Semi-structured and structured data functions,,semi-structured,object,https://docs.snowflake.com/en/sql-reference/functions/object_construct_keep_null
OBJECT_DELETE,"Returns an object containing the contents of the input (that is, source) object with one or more keys removed.",Semi-structured and structured data functions,,semi-structured,object,https://docs.snowflake.com/en/sql-reference/functions/object_delete
OBJECT_INSERT,Returns an OBJECT value consisting of the input OBJECT value with a new key-value pair inserted (or an existing key updated with a new value).,Semi-structured and structured data functions,,semi-structured,object,https://docs.snowflake.com/en/sql-reference/functions/object_insert
OBJECT_KEYS,Returns an array containing the list of keys in the top-most level of the input object.,Semi-structured and structured data functions,,semi-structured,object,https://docs.snowflake.com/en/sql-reference/functions/object_keys
OBJECT_PICK,Returns a new OBJECT containing some of the key-value pairs from an existing object.,Semi-structured and structured data functions,,semi-structured,object,https://docs.snowflake.com/en/sql-reference/functions/object_pick
OCTET_LENGTH,Returns the length of a string or binary value in bytes.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/octet_length
P,,,,,,
PARSE_DOCUMENT (SNOWFLAKE.CORTEX),Returns the extracted content from a document on a Snowflake stage as an OBJECT that contains JSON-encoded objects as strings.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/parse_document-snowflake-cortex
PARSE_IP,Returns a JSON object consisting of all the components from a valid INET (Internet Protocol) or CIDR (Classless Internet Domain Routing) IPv4 or IPv6 string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/parse_ip
PARSE_JSON,"Interprets an input string as a JSON document, producing a VARIANT value.",Semi-structured and structured data functions,,semi-structured,json,https://docs.snowflake.com/en/sql-reference/functions/parse_json
PARSE_URL,"Returns a JSON object consisting of all the components (fragment, host, path, port, query, scheme) in a valid input URL/URI.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/parse_url
PARSE_XML,"Interprets an input string as an XML document, producing an OBJECT value.",Semi-structured and structured data functions,,semi-structured,xml,https://docs.snowflake.com/en/sql-reference/functions/parse_xml
PERCENT_RANK,"Returns the relative rank of a value within a group of values, specified as a percentage ranging from 0.0 to 1.0.",Window functions,,window,,https://docs.snowflake.com/en/sql-reference/functions/percent_rank
PERCENTILE_CONT,Return a percentile value based on a continuous distribution of the input column (specified in order_by_expr).,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/percentile_cont
PERCENTILE_DISC,Returns a percentile value based on a discrete distribution of the input column (specified in order_by_expr).,"Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/percentile_disc
PI,Returns the value of pi as a floating-point value.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/pi
PIPE_USAGE_HISTORY,This table function can be used to query the history of data loaded into Snowflake tables using Snowpipe within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/pipe_usage_history
POLICY_CONTEXT,"Simulates the results of a query based upon the value of one or more context functions, which lets you determine how policies affect query results.",Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/policy_context
POLICY_REFERENCES,Returns a row for each object that has the specified policy assigned to the object or returns a row for each policy assigned to the specified object.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/policy_references
POSITION,"Searches for the first occurrence of the first argument in the second argument and, if successful, returns the position (1-based) of the first argument in the second argument.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/position
"POW, POWER",Returns a number (x) raised to the specified power (y).,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/pow
PREVIOUS_DAY,Returns the date of the first specified DOW (day of week) that occurs before the input date.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/previous_day
Q,,,,,,
QUERY_ACCELERATION_HISTORY,The QUERY_ACCELERATION_HISTORY function is used for querying the query acceleration service history within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/query_acceleration_history
"QUERY_HISTORY , QUERY_HISTORY_BY_*",The QUERY_HISTORY family of table functions can be used to query Snowflake query history along various dimensions.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/query_history
R,,,,,,
RADIANS,Converts degrees to radians.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/radians
RANDOM,Each call returns a pseudo-random 64-bit integer.,Data generation functions,,generation,,https://docs.snowflake.com/en/sql-reference/functions/random
RANDSTR,Returns a random string of specified length.,Data generation functions,,generation,,https://docs.snowflake.com/en/sql-reference/functions/randstr
RANK,Returns the rank of a value within an ordered group of values.,Window functions,,window,,https://docs.snowflake.com/en/sql-reference/functions/rank
RATIO_TO_REPORT,Returns the ratio of a value within a group to the sum of the values within the group.,Window functions,,window,,https://docs.snowflake.com/en/sql-reference/functions/ratio_to_report
REDUCE,Reduces an array to a single value based on the logic in a lambda expression.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/reduce
[ NOT ] REGEXP,Performs a comparison to determine whether a string matches or does not match a specified pattern.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/regexp
REGEXP_COUNT,Returns the number of times that a pattern occurs in a string.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/regexp_count
REGEXP_INSTR,Returns the position of the specified occurrence of the regular expression pattern in the string subject.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/regexp_instr
REGEXP_LIKE,Performs a comparison to determine whether a string matches a specified pattern.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/regexp_like
REGEXP_REPLACE,Returns the subject with the specified pattern (or all occurrences of the pattern) either removed or replaced by a replacement string.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/regexp_replace
REGEXP_SUBSTR,Returns the substring that matches a regular expression within a string.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/regexp_substr
REGEXP_SUBSTR_ALL,Returns an ARRAY that contains all substrings that match a regular expression within a string.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/regexp_substr_all
REGR_AVGX,"Returns the average of the independent variable for non-null pairs in a group, where x is the independent variable and y is the dependent variable.","Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_avgx
REGR_AVGY,"Returns the average of the dependent variable for non-null pairs in a group, where x is the independent variable and y is the dependent variable.","Aggregate functions , Window functions",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_avgy
REGR_COUNT,Returns the number of non-null number pairs in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_count
REGR_INTERCEPT,Returns the intercept of the univariate linear regression line for non-null pairs in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_intercept
REGR_R2,Returns the coefficient of determination for non-null pairs in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_r2
REGR_SLOPE,Returns the slope of the linear regression line for non-null pairs in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_slope
REGR_SXX,"Returns REGR_COUNT(y, x) * VAR_POP(x) for non-null pairs.","Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_sxx
REGR_SXY,"Returns REGR_COUNT(expr1, expr2) * COVAR_POP(expr1, expr2) for non-null pairs.","Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_sxy
REGR_SYY,"Returns REGR_COUNT(y, x) * VAR_POP(y) for non-null pairs.","Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/regr_syy
REGR_VALX,"Returns NULL if the first argument is NULL; otherwise, returns the second argument.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/regr_valx
REGR_VALY,"Returns NULL if the second argument is NULL; otherwise, returns the first argument.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/regr_valy
REPEAT,Builds a string by repeating the input for the specified number of times.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/repeat
REPLACE,"Removes all occurrences of a specified substring, and optionally replaces them with another substring.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/replace
REPLICATION_GROUP_REFRESH_HISTORY,Returns the replication history for a secondary replication or failover group within the last 14 days.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/replication_group_refresh_history
"REPLICATION_GROUP_REFRESH_PROGRESS, REPLICATION_GROUP_REFRESH_PROGRESS_BY_JOB",The REPLICATION_GROUP_REFRESH_PROGRESS family of functions can be used to query the status of a replication or failover group refresh.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/replication_group_refresh_progress
REPLICATION_GROUP_USAGE_HISTORY,Returns the replication usage history for secondary replication or failover groups within the last 14 days.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/replication_group_usage_history
REPLICATION_USAGE_HISTORY,This table function can be used to query the replication history for a specified database within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/replication_usage_history
REST_EVENT_HISTORY,Returns a list of SCIM REST API requests made to Snowflake over a specified time interval.,Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/rest_event_history
RESULT_SCAN,Returns the result set of a previous command (within 24 hours of when you executed the query) as if the result was a table.,Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/result_scan
REVERSE,"Reverses the order of characters in a string, or of bytes in a binary value.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/reverse
RIGHT,Returns a rightmost substring of its input.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/right
[ NOT ] RLIKE,Performs a comparison to determine whether a string matches or does not match a specified pattern.,String functions (regular expressions),,string_binary,regex,https://docs.snowflake.com/en/sql-reference/functions/rlike
ROUND,Returns rounded values for input_expr.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/round
ROW_NUMBER,Returns a unique row number for each row within a window partition.,Window function syntax and usage,,window,,https://docs.snowflake.com/en/sql-reference/functions/row_number
RPAD,"Right-pads a string with characters from another string, or right-pads a binary value with bytes from another binary value.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/rpad
RTRIM,"Removes trailing characters, including whitespace, from a string.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/rtrim
RTRIMMED_LENGTH,"Returns the length of its argument, minus trailing whitespace, but including leading whitespace.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/rtrimmed_length
S,,,,,,
SANITIZE_WEBHOOK_CONTENT,"Removes placeholders (for example, the SNOWFLAKE_WEBHOOK_SECRET placeholder, which specifies a secret) from the body of a notification message to be sent.",Notification functions,,notification,,https://docs.snowflake.com/en/sql-reference/functions/sanitize_webhook_content
SCHEDULED_TIME,Returns the timestamp representing the scheduled time of the current alert.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/scheduled_time
SEARCH,"Searches character data (text) in specified columns from one or more tables, including fields in VARIANT, OBJECT, and ARRAY columns.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/search
SEARCH_IP,"Searches for valid IPv4 addresses in specified character-string columns from one or more tables, including fields in VARIANT, OBJECT, and ARRAY columns.",,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/search_ip
SEARCH_OPTIMIZATION_HISTORY,This table function is used for querying the search optimization service maintenance history for a specified table within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/search_optimization_history
SEARCH_PREVIEW (SNOWFLAKE.CORTEX),"Given a Cortex Search service name, and a query, returns a response from the specified service.",String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/search_preview-snowflake-cortex
SENTIMENT (SNOWFLAKE.CORTEX),Returns a sentiment score for the given English-language input text.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/sentiment-snowflake-cortex
SEQ1 / SEQ2 / SEQ4 / SEQ8,"Returns a sequence of monotonically increasing integers, with wrap-around.",Data generation functions,,generation,,https://docs.snowflake.com/en/sql-reference/functions/seq1
SERVERLESS_ALERT_HISTORY,This table function is used for querying the serverless alert usage history.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/serverless_alert_history
SERVERLESS_TASK_HISTORY,This table function is used for querying the serverless task usage history.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/serverless_task_history
"SHA1 , SHA1_HEX",Returns a 40-character hex-encoded string containing the 160-bit SHA-1 message digest.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/sha1
SHA1_BINARY,Returns a 20-byte binary containing the 160-bit SHA-1 message digest.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/sha1_binary
"SHA2 , SHA2_HEX","Returns a hex-encoded string containing the N-bit SHA-2 message digest, where N is the specified output digest size.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/sha2
SHA2_BINARY,"Returns a binary containing the N-bit SHA-2 message digest, where N is the specified output digest size.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/sha2_binary
SHOW_PYTHON_PACKAGES_DEPENDENCIES,Returns a list of the dependencies and their versions for the Python packages that were specified.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/show_python_packages_dependencies
SIGN,Returns the sign of its argument.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/sign
SIN,Computes the sine of its argument; the argument should be expressed in radians.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/sin
SINH,Computes the hyperbolic sine of its argument.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/sinh
SKEW,Returns the sample skewness of non-NULL records.,Aggregate functions,,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/skew
SOUNDEX,Returns a string that contains a phonetic representation of the input string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/soundex
SOUNDEX_P123,"Returns a string that contains a phonetic representation of the input string, and retains the Soundex code number for the second letter when the first and second letters use the same number.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/soundex_p123
SPACE,Builds a string consisting of the specified number of blank spaces.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/space
SPLIT,Splits a given string with a given separator and returns the result in an array of strings.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/split
SPLIT_PART,Splits a given string at a specified character and returns the requested part.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/split_part
SPLIT_TEXT_RECURSIVE_CHARACTER (SNOWFLAKE.CORTEX),"The SPLIT_TEXT_RECURSIVE_CHARACTER function splits a string into shorter stings, recursively, for preprocessing text to be used with text embedding or search indexing functions.",,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/split_text_recursive_character-snowflake-cortex
SPLIT_TO_TABLE,This table function splits a string (based on a specified delimiter) and flattens the results into rows.,"String & binary functions , Table functions",,table,string,https://docs.snowflake.com/en/sql-reference/functions/split_to_table
SQRT,Returns the square-root of a non-negative numeric expression.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/sqrt
SQUARE,Returns the square of a numeric expression (i.e. a numeric expression multiplied by itself).,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/square
ST_AREA,Returns the area of the Polygon(s) in a GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_area
ST_ASEWKB,"Given a value of type GEOGRAPHY or GEOMETRY, return the binary representation of that value in EWKB (extended well-known binary) format.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_asewkb
ST_ASEWKT,"Given a value of type GEOGRAPHY or GEOMETRY, return the text (VARCHAR) representation of that value in EWKT (extended well-known text) format.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_asewkt
ST_ASGEOJSON,"Given a value of type GEOGRAPHY or GEOMETRY, return the GeoJSON representation of that value.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_asgeojson
"ST_ASWKB , ST_ASBINARY","Given a value of type GEOGRAPHY or GEOMETRY, return the binary representation of that value in WKB (well-known binary) format.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_aswkb
"ST_ASWKT , ST_ASTEXT","Given a value of type GEOGRAPHY or GEOMETRY, return the text (VARCHAR) representation of that value in WKT (well-known text) format.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_aswkt
ST_AZIMUTH,"Given a Point that represents the origin (the location of the observer) and a specified Point, returns the azimuth in radians.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_azimuth
ST_BUFFER,Returns a GEOMETRY object that represents a MultiPolygon containing the points within a specified distance of the input GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_buffer
ST_CENTROID,Returns the Point representing the geometric center of a GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_centroid
ST_COLLECT,There are two forms of ST_COLLECT.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_collect
ST_CONTAINS,Returns TRUE if a GEOGRAPHY or GEOMETRY object is completely inside another object of the same type.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_contains
ST_COVEREDBY,Returns TRUE if no point in one geospatial object is outside another geospatial object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_coveredby
ST_COVERS,Returns TRUE if no point in one geospatial object is outside of another geospatial object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_covers
ST_DIFFERENCE,"Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the points in the first object that are not in the second object (i.e. the difference between the two objects).",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_difference
ST_DIMENSION,"Given a value of type GEOGRAPHY or GEOMETRY, return the “dimension” of the value.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_dimension
ST_DISJOINT,Returns TRUE if the two GEOGRAPHY objects or the two GEOMETRY objects are disjoint (i.e. do not share any portion of space).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_disjoint
ST_DISTANCE,Returns the minimum geodesic distance between two GEOGRAPHY or the minimum Euclidean distance between two GEOMETRY objects.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_distance
ST_DWITHIN,Returns TRUE if the minimum geodesic distance between two points (two GEOGRAPHY objects) is within the specified distance.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_dwithin
ST_ENDPOINT,Returns the last Point in a LineString.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_endpoint
ST_ENVELOPE,Returns the minimum bounding box (a rectangular “envelope”) that encloses a specified GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_envelope
ST_GEOGFROMGEOHASH,Returns a GEOGRAPHY object for the polygon that represents the boundaries of a geohash.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/st_geogfromgeohash
ST_GEOGPOINTFROMGEOHASH,Returns a GEOGRAPHY object for the Point that represents the center of a geohash.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/st_geogpointfromgeohash
ST_GEOGRAPHYFROMWKB,Parses a WKB (well-known binary) or EWKB (extended well-known binary) input and returns a value of type GEOGRAPHY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/st_geographyfromwkb
ST_GEOGRAPHYFROMWKT,Parses a WKT (well-known text) or EWKT (extended well-known text) input and returns a value of type GEOGRAPHY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/st_geographyfromwkt
ST_GEOHASH,Returns the geohash for a GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_geohash
ST_GEOMETRYFROMWKB,Parses a WKB (well-known binary) or EWKB (extended well-known binary) input and returns a value of type GEOMETRY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/st_geometryfromwkb
ST_GEOMETRYFROMWKT,Parses a WKT (well-known text) or EWKT (extended well-known text) input and returns a value of type GEOMETRY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/st_geometryfromwkt
ST_GEOMFROMGEOHASH,Returns a GEOMETRY object for the polygon that represents the boundaries of a geohash.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_geomfromgeohash
ST_GEOMPOINTFROMGEOHASH,Returns a GEOMETRY object for the point that represents center of a geohash.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_geompointfromgeohash
ST_HAUSDORFFDISTANCE,Returns the discrete Hausdorff distance between two GEOGRAPHY objects.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_hausdorffdistance
ST_INTERPOLATE,"Given an input GEOGRAPHY object, returns an interpolated object that is within a specified tolerance.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_interpolate
ST_INTERSECTION,"Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the shape containing the set of points that are common to both input objects (i.e. the intersection of the two objects).",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_intersection
ST_INTERSECTION_AGG,"Given a GEOGRAPHY column, returns a GEOGRAPHY object that represents the shape containing the combined set of points that are common to the shapes represented by the objects in the column (that is, the intersection of the shapes).",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_intersection_agg
ST_INTERSECTS,Returns TRUE if the two GEOGRAPHY objects or the two GEOMETRY objects intersect (i.e. share any portion of space).,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_intersects
ST_ISVALID,Returns TRUE if the specified GEOGRAPHY or GEOMETRY object represents a valid shape.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_isvalid
ST_LENGTH,Returns the geodesic length of the LineString(s) in a GEOGRAPHY object or the Euclidean length of the LineString(s) in a GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_length
"ST_MAKEGEOMPOINT , ST_GEOMPOINT",Constructs a GEOMETRY object that represents a Point with the specified longitude and latitude.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_makegeompoint
ST_MAKELINE,Constructs a GEOGRAPHY or GEOMETRY object that represents a line connecting the points in the input objects.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_makeline
"ST_MAKEPOINT , ST_POINT",Constructs a GEOGRAPHY object that represents a point with the specified longitude and latitude.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_makepoint
"ST_MAKEPOLYGON , ST_POLYGON",Constructs a GEOGRAPHY or GEOMETRY object that represents a Polygon without holes.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_makepolygon
ST_MAKEPOLYGONORIENTED,Constructs a GEOGRAPHY object that represents a Polygon without holes.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_makepolygonoriented
"ST_NPOINTS , ST_NUMPOINTS",Returns the number of points in a GEOGRAPHY or GEOGRAPHY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_npoints
ST_PERIMETER,Returns the length of the perimeter of the polygon(s) in a GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_perimeter
ST_POINTN,Returns a Point at a specified index in a LineString.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_pointn
ST_SETSRID,Returns a GEOMETRY object that has its SRID (spatial reference system identifier) set to the specified value.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_setsrid
ST_SIMPLIFY,"Given an input GEOGRAPHY or GEOMETRY object that represents a Line or Polygon, returns a simpler approximation of the object.",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_simplify
ST_SRID,Returns the SRID (spatial reference system identifier) of a GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_srid
ST_STARTPOINT,Returns the first Point in a LineString.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_startpoint
ST_SYMDIFFERENCE,"Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the set of points from both input objects that are not part of the intersection of the objects (i.e. the symmetric difference of the two objects).",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_symdifference
ST_TRANSFORM,Converts a GEOMETRY object from one spatial reference system (SRS) to another.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_transform
ST_UNION,"Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the combined set of shapes for both objects (i.e. the union of the two shapes).",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_union
ST_UNION_AGG,"Given a GEOGRAPHY column, returns a GEOGRAPHY object that represents the combined set of points that are in at least one of the shapes represented by the objects in the column (that is, the union of the shapes).",Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_union_agg
ST_WITHIN,Returns true if the first geospatial object is fully contained by the second geospatial object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_within
ST_X,Returns the longitude (X coordinate) of a Point represented by a GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_x
ST_XMAX,Returns the maximum longitude (X coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_xmax
ST_XMIN,Returns the minimum longitude (X coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_xmin
ST_Y,Returns the latitude (Y coordinate) of a Point represented by a GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_y
ST_YMAX,Returns the maximum latitude (Y coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_ymax
ST_YMIN,Returns the minimum latitude (Y coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object.,Geospatial functions,,geospatial,,https://docs.snowflake.com/en/sql-reference/functions/st_ymin
STAGE_DIRECTORY_FILE_REGISTRATION_HISTORY,This table function can be used to query information about the metadata history for a directory table.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/stage_directory_file_registration_history
STAGE_STORAGE_USAGE_HISTORY,"This table function can be used to query the average daily data storage usage, in bytes, for all the Snowflake stages in your account within a specified date range.","Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/stage_storage_usage_history
STARTSWITH,Returns true if expr1 starts with expr2.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/startswith
"STDDEV, STDDEV_SAMP",Returns the sample standard deviation (square root of sample variance) of non-NULL values.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/stddev
STDDEV_POP,Returns the population standard deviation (square root of variance) of non-NULL values.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/stddev_pop
STRIP_NULL_VALUE,Converts a JSON null value to a SQL NULL value.,Semi-structured and structured data functions,,semi-structured,json,https://docs.snowflake.com/en/sql-reference/functions/strip_null_value
STRTOK,Tokenizes a given string and returns the requested part.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/strtok
STRTOK_SPLIT_TO_TABLE,Tokenizes a string with the given set of delimiters and flattens the results into rows.,"String & binary functions , Table functions",,table,string,https://docs.snowflake.com/en/sql-reference/functions/strtok_split_to_table
STRTOK_TO_ARRAY,Tokenizes the given string using the given set of delimiters and returns the tokens as an array.,"String & binary functions , Semi-structured and structured data functions",,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/strtok_to_array
"SUBSTR , SUBSTRING","Returns the portion of the string or binary value from base_expr, starting from the character/byte specified by start_expr, with optionally limited length.",String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/substr
SUM,Returns the sum of non-NULL records for expr.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/sum
SUMMARIZE (SNOWFLAKE.CORTEX),Summarizes the given English-language input text.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/summarize-snowflake-cortex
SYSDATE,Returns the current timestamp for the system in the UTC time zone.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/sysdate
SYSTEM$ABORT_SESSION,Aborts the specified session.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_abort_session
SYSTEM$ABORT_TRANSACTION,"Aborts the specified transaction, if it is running.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_abort_transaction
SYSTEM$ADD_EVENT (for Snowflake Scripting),Add an event for trace.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_add_event
SYSTEM$ALLOWLIST,Returns hostnames and port numbers to add to your firewall’s allowed list so that you can access Snowflake from behind your firewall.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_allowlist
SYSTEM$ALLOWLIST_PRIVATELINK,"Returns hostnames and port numbers for AWS PrivateLink, Azure Private Link, and Google Cloud Private Service Connect deployments to add to your firewall’s allowed list so that you can access Snowflake from behind your firewall.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_allowlist_privatelink
SYSTEM$AUTHORIZE_PRIVATELINK,Enables private connectivity to the Snowflake service for the current account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_authorize_privatelink
SYSTEM$AUTHORIZE_STAGE_PRIVATELINK_ACCESS,Authorizes Snowflake to access the Microsoft Azure Private Endpoint for Azure private endpoints for internal stages for the current account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_authorize_stage_privatelink_access
SYSTEM$AUTO_REFRESH_STATUS,Returns the automated refresh status for an externally managed Iceberg table.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_auto_refresh_status
SYSTEM$BEHAVIOR_CHANGE_BUNDLE_STATUS,Returns the status of the specified behavior change release bundle for the current account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_behavior_change_bundle_status
SYSTEM$BLOCK_INTERNAL_STAGES_PUBLIC_ACCESS,Prevents all public traffic from accessing the internal stage of the current Snowflake account on Microsoft Azure.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_block_internal_stages_public_access
SYSTEM$CANCEL_ALL_QUERIES,Cancels all active/running queries in the specified session.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_cancel_all_queries
SYSTEM$CANCEL_QUERY,Cancels the specified query (or statement) if it is currently active/running.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_cancel_query
SYSTEM$CLEANUP_DATABASE_ROLE_GRANTS,Revokes privileges on dropped objects from the share and grants the database role to the share.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_cleanup_database_role_grants
SYSTEM$CLIENT_VERSION_INFO,Returns version information for Snowflake clients and drivers.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_client_version_info
SYSTEM$CLUSTERING_DEPTH,Computes the average depth of the table according to the specified columns (or the clustering key defined for the table).,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_clustering_depth
SYSTEM$CLUSTERING_INFORMATION,"Returns clustering information, including average clustering depth, for a table based on one or more columns in the table.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_clustering_information
SYSTEM$CLUSTERING_RATIO — Deprecated,"Calculates the clustering ratio for a table, based on one or more columns in the table.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_clustering_ratio
SYSTEM$COMMIT_MOVE_ORGANIZATION_ACCOUNT,Finalizes the process of moving an organization account from one region to another.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_commit_move_organization_account
SYSTEM$CONVERT_PIPES_SQS_TO_SNS,Convert pipes using Amazon SQS (Simple Queue Service) notifications to the Amazon Simple Notification Service (SNS) service for an S3 bucket.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_convert_pipes_sqs_to_sns
SYSTEM$CREATE_BILLING_EVENT,Creates a billable event that tracks consumer usage of an installed monetized application.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_create_billing_event
SYSTEM$CREATE_BILLING_EVENTS,Creates multiple billable events that track consumer usage of installed monetized applications.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_create_billing_events
SYSTEM$CURRENT_USER_TASK_NAME,Returns the name of the task currently executing when invoked from the statement or stored procedure defined by the task.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_current_user_task_name
SYSTEM$DATA_METRIC_SCAN,Returns the rows identified by a data quality metric as containing data that failed a data quality check.,"System functions , Table functions",,system,,https://docs.snowflake.com/en/sql-reference/functions/system_data_metric_scan
SYSTEM$DATABASE_REFRESH_HISTORY — Deprecated,Returns a JSON object showing the refresh history for a secondary database.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_database_refresh_history
"SYSTEM$DATABASE_REFRESH_PROGRESS , SYSTEM$DATABASE_REFRESH_PROGRESS_BY_JOB — Deprecated",The SYSTEM$DATABASE_REFRESH_PROGRESS family of functions can be used to query the status of a database refresh along various dimensions.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_database_refresh_progress
SYSTEM$DEPROVISION_PRIVATELINK_ENDPOINT,Deprovisions a private connectivity endpoint in the Snowflake VPC or VNet to prevent Snowflake from connecting to an external service using private connectivity.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_deprovision_privatelink_endpoint
SYSTEM$DISABLE_BEHAVIOR_CHANGE_BUNDLE,Disables the behavior changes included in the specified behavior change release bundle for the current account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_disable_behavior_change_bundle
SYSTEM$DISABLE_DATABASE_REPLICATION,Disable replication for a primary database and any secondary databases linked to it.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_disable_database_replication
SYSTEM$DISABLE_PREVIEW_ACCESS,Disables access to open preview and private preview features.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_disable_preview_access
SYSTEM$ENABLE_BEHAVIOR_CHANGE_BUNDLE,Enables behavior changes included in the specified behavior change release bundle for the current account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_enable_behavior_change_bundle
SYSTEM$ENABLE_PREVIEW_ACCESS,Enables access to open preview features.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_enable_preview_access
SYSTEM$ESTIMATE_AUTOMATIC_CLUSTERING_COSTS,Returns estimated costs associated with enabling Automatic Clustering for a table.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_estimate_automatic_clustering_costs
SYSTEM$ESTIMATE_QUERY_ACCELERATION,"For a previously executed query, this function returns a JSON object that specifies if the query is eligible to benefit from the query acceleration service.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_estimate_query_acceleration
SYSTEM$ESTIMATE_SEARCH_OPTIMIZATION_COSTS,Returns the estimated costs of adding search optimization to a given table and configuring specific columns for search optimization.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_estimate_search_optimization_costs
SYSTEM$EXPLAIN_JSON_TO_TEXT,This function converts EXPLAIN output from JSON to formatted text.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_explain_json_to_text
SYSTEM$EXPLAIN_PLAN_JSON,"Given the text of a SQL statement, this function generates the EXPLAIN plan in JSON.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_explain_plan_json
SYSTEM$EXTERNAL_TABLE_PIPE_STATUS,Retrieves a JSON representation of the current refresh status for the internal (hidden) pipe object associated with an external table.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_external_table_pipe_status
SYSTEM$FINISH_OAUTH_FLOW,Sets the OAUTH_REFRESH_TOKEN parameter value of the secret passed as an argument in the SYSTEM$START_OAUTH_FLOW call that began the OAuth flow.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_finish_oauth_flow
SYSTEM$GENERATE_SAML_CSR,Generates a certificate signing request (CSR) with the subject set to the subject of the certificate stored in the SAML2 integration and can specify the DN to be used in the CSR.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_generate_saml_csr
SYSTEM$GENERATE_SCIM_ACCESS_TOKEN,Returns a new SCIM access token that is valid for six months.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_generate_scim_access_token
SYSTEM$GET_AWS_SNS_IAM_POLICY,Returns an AWS IAM policy statement that must be added to the Amazon SNS topic policy in order to grant the Amazon SQS messaging queue created by Snowflake to subscribe to the topic.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_aws_sns_iam_policy
SYSTEM$GET_CLASSIFICATION_RESULT,Returns the classification result of the specified object.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_classification_result
SYSTEM$GET_CMK_AKV_CONSENT_URL,Returns a consent URL to the Azure Key Vault account related to customer-managed keys.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_akv_consent_url
SYSTEM$GET_CMK_CONFIG,Returns configuration information for use with customer-managed keys (CMKs) and Tri-Secret Secure.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_config
SYSTEM$GET_CMK_INFO,Returns a status about your customer-managed key (CMK) for use with Tri-Secret Secure.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_info
SYSTEM$GET_CMK_KMS_KEY_POLICY,Returns an ARRAY containing a snippet of the AWS Key Management Service policy information related to customer-managed keys.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_kms_key_policy
SYSTEM$GET_COMPUTE_POOL_STATUS,Retrieves status of a compute pool.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_compute_pool_status
SYSTEM$GET_DIRECTORY_TABLE_STATUS,Returns a list of records that contain the directory table consistency status for stages in your account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_directory_table_status
SYSTEM$GET_GCP_KMS_CMK_GRANT_ACCESS_CMD,Returns a Google Cloud gcloud command to obtain policy information for the Google Cloud Key Management Service for use with customer-managed keys.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_gcp_kms_cmk_grant_access_cmd
SYSTEM$GET_ICEBERG_TABLE_INFORMATION,Returns the location of the root metadata file and status of the latest snapshot for an Apache Iceberg™ table.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_iceberg_table_information
SYSTEM$GET_LOGIN_FAILURE_DETAILS,"Returns a JSON object that represents an unsuccessful login attempt associated with External OAuth, SAML, or key pair authentication.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_login_failure_details
SYSTEM$GET_PREDECESSOR_RETURN_VALUE,Retrieves the return value for the predecessor task in a task graph.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_predecessor_return_value
SYSTEM$GET_PREVIEW_ACCESS_STATUS,Determine if access to all preview features is enabled or disabled.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_preview_access_status
SYSTEM$GET_PRIVATELINK,Verifies whether your current account is authorized for private connectivity to the Snowflake service.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink
SYSTEM$GET_PRIVATELINK_AUTHORIZED_ENDPOINTS,Returns a list of the authorized endpoints for your current account to use with private connectivity to the Snowflake service.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink_authorized_endpoints
SYSTEM$GET_PRIVATELINK_CONFIG,Returns a JSON representation of the Snowflake account information necessary to facilitate the self-service configuration of private connectivity to the Snowflake service or Snowflake internal stages.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink_config
SYSTEM$GET_PRIVATELINK_ENDPOINTS_INFO,Returns the status of all private connectivity endpoints that you provision.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink_endpoints_info
SYSTEM$GET_SERVICE_LOGS,Retrieves local logs from a Snowpark Container Services service container.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_service_logs
SYSTEM$GET_SERVICE_STATUS — Deprecated,Retrieves the status of a Snowpark Container Services service.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_service_status
SYSTEM$GET_SNOWFLAKE_PLATFORM_INFO,Returns platform information for the cloud provider that hosts your Snowflake account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_snowflake_platform_info
SYSTEM$GET_TAG,Returns the tag value associated with the specified Snowflake object or column.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_tag
SYSTEM$GET_TAG_ALLOWED_VALUES,"Returns a comma-separated list of string values that can be set on a supported object, or NULL to indicate the tag key does not have any specified string values and accepts all possible string values.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_tag_allowed_values
SYSTEM$GET_TAG_ON_CURRENT_COLUMN,Returns the tag string value assigned to the column based upon the specified tag or NULL if a tag is not assigned to the specified column.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_tag_on_current_column
SYSTEM$GET_TAG_ON_CURRENT_TABLE,Returns the tag string value assigned to the table based upon the specified tag or NULL if a tag is not assigned to the specified table.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_tag_on_current_table
SYSTEM$GET_TASK_GRAPH_CONFIG,Returns the value of the configuration string for the task currently executing when invoked from the statement or stored procedure defined by the task.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_get_task_graph_config
SYSTEM$GLOBAL_ACCOUNT_SET_PARAMETER,Enables replication and failover features for a specified account in an organization.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_global_account_set_parameter
SYSTEM$INITIATE_MOVE_ORGANIZATION_ACCOUNT,Starts the process of moving an organization account to a new region.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_initiate_move_organization_account
SYSTEM$INTERNAL_STAGES_PUBLIC_ACCESS_STATUS,Checks to see whether public IP addresses are allowed to access the internal stage of the current Snowflake account on Microsoft Azure.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_internal_stages_public_access_status
SYSTEM$IS_APPLICATION_INSTALLED_FROM_SAME_ACCOUNT,Shows if an app is installed on the same account as the application package it is based on.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_is_application_installed_from_same_account
SYSTEM$IS_APPLICATION_SHARING_EVENTS_WITH_PROVIDER,Shows if event sharing is enabled.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_is_application_sharing_events_with_provider
SYSTEM$LAST_CHANGE_COMMIT_TIME,Returns a token that can be used to detect whether a database table or view changed between two calls to the function.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_last_change_commit_time
SYSTEM$LINK_ACCOUNT_OBJECTS_BY_NAME,Adds a global identifier to account objects in the target (current) account that were created using scripts and that match objects with the same names in the source account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_link_account_objects_by_name
SYSTEM$LIST_APPLICATION_RESTRICTED_FEATURES,Returns a JSON object containing a list of restricted features that the consumer has allowed a,native-app,,system,native-app,https://docs.snowflake.com/en/sql-reference/functions/system_list_application_restricted_features
SYSTEM$LIST_ICEBERG_TABLES_FROM_CATALOG,Lists tables in a remote,iceberg-tm,,system,iceberg,https://docs.snowflake.com/en/sql-reference/functions/system_list_iceberg_tables_from_catalog
SYSTEM$LIST_NAMESPACES_FROM_CATALOG,Lists the namespaces in a remote,iceberg-tm,,system,iceberg,https://docs.snowflake.com/en/sql-reference/functions/system_list_namespaces_from_catalog
SYSTEM$LOG, SYSTEM$LOG_<level> (for Snowflake Scripting)",Logs a message at the specified severity level.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_log
SYSTEM$MIGRATE_SAML_IDP_REGISTRATION,Migrates an existing SAML identity provider (i.e. IdP) configuration as defined by the account parameter SAML_IDENTITY_PROVIDER to a security integration.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_migrate_saml_idp_registration
SYSTEM$PIPE_FORCE_RESUME,Forces a pipe paused using ALTER PIPE to resume.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_pipe_force_resume
SYSTEM$PIPE_REBINDING_WITH_NOTIFICATION_CHANNEL,Retries the notification channel binding process when a replicated pipe has not been successfully bound to a notification channel during replication time.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_pipe_rebinding_with_notification_channel
SYSTEM$PIPE_STATUS,Retrieves a JSON representation of the current status of a pipe.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_pipe_status
SYSTEM$PROVISION_PRIVATELINK_ENDPOINT,Provisions a private connectivity endpoint in the Snowflake VPC or VNet to enable Snowflake to connect to an external service using private connectivity.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_provision_privatelink_endpoint
SYSTEM$QUERY_REFERENCE,Returns a query reference that you can pass to a stored procedure.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_query_reference
SYSTEM$REFERENCE,"Returns a reference to an object (a table, view, or function).",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_reference
SYSTEM$REGISTER_CMK_INFO,Registers your customer-managed key (CMK) for use with Tri-Secret Secure.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_register_cmk_info
SYSTEM$REGISTRY_LIST_IMAGES — Deprecated,Lists images in an image repository.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_registry_list_images
SYSTEM$RESTORE_PRIVATELINK_ENDPOINT,Restores a private connectivity endpoint in the Snowflake VPC or VNet to enable Snowflake to connect to an external service using private connectivity.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_restore_privatelink_endpoint
SYSTEM$REVOKE_PRIVATELINK,Disables private connectivity to the Snowflake service for the current account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_revoke_privatelink
SYSTEM$REVOKE_STAGE_PRIVATELINK_ACCESS,Revokes the authorization for Snowflake to access the Microsoft Azure Private Endpoint for Azure private endpoints for internal stages for the current account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_revoke_stage_privatelink_access
SYSTEM$SEND_NOTIFICATIONS_TO_CATALOG,Sends a notification to Snowflake Open Catalog to update Snowflake-managed Apache Iceberg™ tables in,opencatalog,,system,opencatalog,https://docs.snowflake.com/en/sql-reference/functions/system_send_notifications_to_catalog
SYSTEM$SET_APPLICATION_RESTRICTED_FEATURE_ACCESS,Enables a restricted feature for a,native-app,,system,native-app,https://docs.snowflake.com/en/sql-reference/functions/system_set_application_restricted_feature_access
SYSTEM$SET_EVENT_SHARING_ACCOUNT_FOR_REGION,Sets the event account for a region.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_set_event_sharing_account_for_region
SYSTEM$SET_RETURN_VALUE,Explicitly sets the return value for a task.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_set_return_value
SYSTEM$SET_SPAN_ATTRIBUTES (for Snowflake Scripting),Sets attribute name and value associated with a span containing trace events.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_set_span_attributes
SYSTEM$SHOW_ACTIVE_BEHAVIOR_CHANGE_BUNDLES,"Returns an array of the currently available behavior change release bundles, the default state of each bundle, and the actual state of the bundle for the current account.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_show_active_behavior_change_bundles
SYSTEM$SHOW_BUDGETS_IN_ACCOUNT,Returns the budgets in the account for which you have access privileges.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_show_budgets_in_account
SYSTEM$SHOW_EVENT_SHARING_ACCOUNTS,Shows event accounts in a provider organization.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_show_event_sharing_accounts
SYSTEM$SHOW_MOVE_ORGANIZATION_ACCOUNT_STATUS,Returns the status of an attempt to move an organization account.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_show_move_organization_account_status
SYSTEM$SHOW_OAUTH_CLIENT_SECRETS,Returns the client secrets in a string.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_show_oauth_client_secrets
SYSTEM$SNOWPIPE_STREAMING_UPDATE_CHANNEL_OFFSET_TOKEN,Updates the offset token for a particular channel used by Snowpipe Streaming with a new offset token.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_snowpipe_streaming_update_channel_offset_token
SYSTEM$START_OAUTH_FLOW,"Initiates the OAUTH client flow, returning a URL you use in a browser to complete the OAuth consent process.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_start_oauth_flow
SYSTEM$STREAM_BACKLOG,Returns the set of table versions between the current offset for a specified stream and the current timestamp.,"Table functions , System functions",,system,,https://docs.snowflake.com/en/sql-reference/functions/system_stream_backlog
SYSTEM$STREAM_GET_TABLE_TIMESTAMP,Returns the timestamp in nanoseconds of the latest table version at or before the current offset for the specified stream.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_stream_get_table_timestamp
SYSTEM$STREAM_HAS_DATA,Indicates whether a specified stream contains change data capture (CDC) records.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_stream_has_data
SYSTEM$TASK_DEPENDENTS_ENABLE,Recursively resumes a specified task and all its dependent tasks.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_task_dependents_enable
SYSTEM$TASK_RUNTIME_INFO,Returns information about the current task run.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_task_runtime_info
SYSTEM$TYPEOF,Returns a string representing the SQL data type associated with an expression.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_typeof
SYSTEM$UNBLOCK_INTERNAL_STAGES_PUBLIC_ACCESS,Allows traffic from public IP addresses to access the internal stage of the current Snowflake account on Microsoft Azure.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_unblock_internal_stages_public_access
SYSTEM$UNSET_EVENT_SHARING_ACCOUNT_FOR_REGION,Unsets the events account for a region.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_unset_event_sharing_account_for_region
SYSTEM$USER_TASK_CANCEL_ONGOING_EXECUTIONS,Aborts a run of the specified task that the system has already started to process (i.e. a run with an EXECUTING state in the TASK_HISTORY output).,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_user_task_cancel_ongoing_executions
SYSTEM$VALIDATE_STORAGE_INTEGRATION,Validates the configuration for a specified storage integration.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_validate_storage_integration
SYSTEM$VERIFY_CMK_INFO,Verifies your customer-managed key (CMK) configuration and returns a message about the registered CMK.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_verify_cmk_info
SYSTEM$VERIFY_EXTERNAL_OAUTH_TOKEN,Determines whether your External OAuth access token is valid or has expired and needs to be regenerated.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_verify_ext_oauth_token
SYSTEM$VERIFY_EXTERNAL_VOLUME,Verifies the configuration for a specified external volume.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_verify_external_volume
SYSTEM$WAIT,Waits for the specified amount of time before proceeding.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_wait
SYSTEM$WAIT_FOR_SERVICES,Waits for one or more Snowpark Container Services services to reach the READY state (or becomes upgraded) before returning.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_wait_for_services
SYSTEM$WHITELIST — Deprecated,Returns hostnames and port numbers to add to your firewall’s allowed list so that you can access Snowflake from behind your firewall.,System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_whitelist
SYSTEM$WHITELIST_PRIVATELINK — Deprecated,"Returns hostnames and port numbers for AWS PrivateLink, Azure Private Link, and Google Cloud Private Service Connect deployments to add to your firewall’s allowed list so that you can access Snowflake from behind your firewall.",System functions,,system,,https://docs.snowflake.com/en/sql-reference/functions/system_whitelist_privatelink
SYSTIMESTAMP,Returns the current timestamp for the system.,Context functions,,context,,https://docs.snowflake.com/en/sql-reference/functions/systimestamp
T,,,,,,
TAG_REFERENCES,Returns a table in which each row displays an association between a tag and value.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/tag_references
TAG_REFERENCES_ALL_COLUMNS,Returns a table in which each row displays the tag name and tag value assigned to a specific column.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/tag_references_all_columns
TAG_REFERENCES_WITH_LINEAGE,Returns a table in which each row displays an association between the specified tag and the Snowflake object to which the tag is associated.,"Account Usage table functions , Table functions",,account,,https://docs.snowflake.com/en/sql-reference/functions/tag_references_with_lineage
TAN,Computes the tangent of its argument; the argument should be expressed in radians.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/tan
TANH,Computes the hyperbolic tangent of its argument.,Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/tanh
TASK_DEPENDENTS,This table function returns the list of child tasks for a given root task in a task graph.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/task_dependents
TASK_HISTORY,You can use this table function to query the history of task usage within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/task_history
TEXT_HTML,Returns a JSON object that specifies the HTML message to use for a notification.,Notification functions,,notification,,https://docs.snowflake.com/en/sql-reference/functions/text_html
TEXT_PLAIN,Returns a JSON object that specifies the plain text message to use for a notification.,Notification functions,,notification,,https://docs.snowflake.com/en/sql-reference/functions/text_plain
TIME_FROM_PARTS,Creates a time from individual numeric components.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/time_from_parts
TIME_SLICE,"Calculates the beginning or end of a “slice” of time, where the length of the slice is a multiple of a standard unit of time (minute, hour, day, etc.).",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/time_slice
TIMEADD,"Adds the specified value for the specified date or time part to a date, time, or timestamp.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/timeadd
TIMEDIFF,"Calculates the difference between two date, time, or timestamp expressions based on the specified date or time part.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/timediff
TIMESTAMP_FROM_PARTS,Creates a timestamp from individual numeric components.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/timestamp_from_parts
TIMESTAMPADD,"Adds the specified value for the specified date or time part to a date, time, or timestamp.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/timestampadd
TIMESTAMPDIFF,"Calculates the difference between two date, time, or timestamp expressions based on the specified date or time part.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/timestampdiff
TO_ARRAY,Converts the input expression to an ARRAY.,"Conversion functions , Semi-structured and structured data functions",,conversion,semi-structured,https://docs.snowflake.com/en/sql-reference/functions/to_array
TO_BINARY,Converts the input expression to a binary value.,Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/to_binary
TO_BOOLEAN,Converts the input text or numeric expression to a BOOLEAN value.,Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/to_boolean
"TO_CHAR , TO_VARCHAR",Converts the input expression to a string.,Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/to_char
"TO_DATE , DATE",Converts an input expression to a date.,"Conversion functions , Date & time functions",,conversion,datetime,https://docs.snowflake.com/en/sql-reference/functions/to_date
"TO_DECIMAL , TO_NUMBER , TO_NUMERIC",Converts an input expression to a fixed-point number.,Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/to_decimal
TO_DOUBLE,Converts an expression to a double-precision floating-point number.,Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/to_double
TO_GEOGRAPHY,Parses an input and returns a value of type GEOGRAPHY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/to_geography
TO_GEOMETRY,Parses an input and returns a value of type GEOMETRY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/to_geometry
TO_JSON,Converts a VARIANT value to a string containing the JSON representation of the value.,"Conversion functions , Semi-structured and structured data functions",,conversion,semi-structured,https://docs.snowflake.com/en/sql-reference/functions/to_json
TO_OBJECT,Converts the input value to an OBJECT.,"Conversion functions , Semi-structured and structured data functions",,conversion,semi-structured,https://docs.snowflake.com/en/sql-reference/functions/to_object
TO_QUERY,Returns a result set based on SQL text and an optional set of arguments that are passed to the SQL text if it is parameterized.,Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/to_query
"TO_TIME , TIME",Converts an input expression into a time.,"Conversion functions , Date & time functions",,conversion,datetime,https://docs.snowflake.com/en/sql-reference/functions/to_time
TO_TIMESTAMP / TO_TIMESTAMP_*,Converts an input expression into the corresponding timestamp.,"Conversion functions , Date & time functions",,conversion,datetime,https://docs.snowflake.com/en/sql-reference/functions/to_timestamp
TO_VARIANT,Converts any value to a VARIANT value or NULL (if input is NULL).,Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/to_variant
TO_XML,Converts a VARIANT to a VARCHAR that contains an XML representation of the value.,"Conversion functions , Semi-structured and structured data functions",,conversion,semi-structured,https://docs.snowflake.com/en/sql-reference/functions/to_xml
TRANSFORM,Transforms an array based on the logic in a lambda expression.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/transform
TRANSLATE (SNOWFLAKE.CORTEX),Translates the given input text from one supported language to another.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/translate-snowflake-cortex
TRANSLATE,Replaces characters in a string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/translate
TRIM,Removes leading and trailing characters from a string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/trim
"TRUNCATE , TRUNC","Rounds the input expression down to the nearest (or equal) integer closer to zero, or to the nearest equal or smaller value with the specified number of places after the decimal point.",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/trunc
TRUNC,"Truncates a DATE, TIME, or TIMESTAMP value to the specified precision.",Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/trunc2
TRY_BASE64_DECODE_BINARY,A special version of BASE64_DECODE_BINARY that returns a NULL value if an error occurs during decoding.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/try_base64_decode_binary
TRY_BASE64_DECODE_STRING,A special version of BASE64_DECODE_STRING that returns a NULL value if an error occurs during decoding.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/try_base64_decode_string
TRY_CAST,"A special version of CAST , :: that is available for a subset of data type conversions.",Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/try_cast
TRY_COMPLETE (SNOWFLAKE.CORTEX),Performs the same operation as the COMPLETE function but returns NULL instead of raising an error when the operation cannot be performed.,String & binary functions,,string_binary,llm,https://docs.snowflake.com/en/sql-reference/functions/try_complete-snowflake-cortex
TRY_DECRYPT,A special version of DECRYPT that returns a NULL value if an error occurs during decryption.,Encryption functions,,encryption,,https://docs.snowflake.com/en/sql-reference/functions/try_decrypt
TRY_DECRYPT_RAW,A special version of DECRYPT_RAW that returns a NULL value if an error occurs during decryption.,Encryption functions,,encryption,,https://docs.snowflake.com/en/sql-reference/functions/try_decrypt_raw
TRY_HEX_DECODE_BINARY,A special version of HEX_DECODE_BINARY that returns a NULL value if an error occurs during decoding.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/try_hex_decode_binary
TRY_HEX_DECODE_STRING,A special version of HEX_DECODE_STRING that returns a NULL value if an error occurs during decoding.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/try_hex_decode_string
TRY_PARSE_JSON,A special version of PARSE_JSON that returns a NULL value if an error occurs during parsing.,Semi-structured and structured data functions,,semi-structured,json,https://docs.snowflake.com/en/sql-reference/functions/try_parse_json
TRY_TO_BINARY,"A special version of TO_BINARY that performs the same operation (i.e. converts an input expression to a binary value), but with error handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error).",Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/try_to_binary
TRY_TO_BOOLEAN,"A special version of TO_BOOLEAN that performs the same operation (that is, converts an input expression to a Boolean value), but with error-handling support.",Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/try_to_boolean
TRY_TO_DATE,"A special version of the TO_DATE function that performs the same operation (i.e. converts an input expression to a date), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error).","Conversion functions , Date & time functions",,conversion,datetime,https://docs.snowflake.com/en/sql-reference/functions/try_to_date
"TRY_TO_DECIMAL, TRY_TO_NUMBER, TRY_TO_NUMERIC","A special version of TO_DECIMAL , TO_NUMBER , TO_NUMERIC that performs the same operation (i.e. converts an input expression to a fixed-point number), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error).",Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/try_to_decimal
TRY_TO_DOUBLE,"A special version of TO_DOUBLE that performs the same operation (that is, converts an input expression to a double-precision floating-point number), but with error-handling support (that is, if the conversion can’t be performed, it returns a NULL value instead of raising an error).",Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/try_to_double
TRY_TO_GEOGRAPHY,Parses an input and returns a value of type GEOGRAPHY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/try_to_geography
TRY_TO_GEOMETRY,Parses an input and returns a value of type GEOMETRY.,"Geospatial functions , Conversion functions",,conversion,geospatial,https://docs.snowflake.com/en/sql-reference/functions/try_to_geometry
TRY_TO_TIME,"A special version of TO_TIME , TIME that performs the same operation (i.e. converts an input expression into a time), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error).",Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/try_to_time
TRY_TO_TIMESTAMP / TRY_TO_TIMESTAMP_*,"A special version of TO_TIMESTAMP / TO_TIMESTAMP_* that performs the same operation (i.e. converts an input expression into a timestamp), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error).",Conversion functions,,conversion,,https://docs.snowflake.com/en/sql-reference/functions/try_to_timestamp
TYPEOF,Returns the type of a value stored in a VARIANT column.,Semi-structured and structured data functions,,semi-structured,,https://docs.snowflake.com/en/sql-reference/functions/typeof
U,,,,,,
UNICODE,Returns the Unicode code point for the first Unicode character in a string.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/unicode
UNIFORM,"Generates a uniformly-distributed pseudo-random number in the inclusive range [min, max].",Data generation functions,,generation,,https://docs.snowflake.com/en/sql-reference/functions/uniform
UPPER,Returns the input string with all characters converted to uppercase.,String & binary functions,,string_binary,,https://docs.snowflake.com/en/sql-reference/functions/upper
UUID_STRING,Generates either a version 4 (random) or version 5 (named) RFC 4122-compliant universally unique identifier (UUID) as a formatted string.,"String & binary functions , Data generation functions",,generation,string,https://docs.snowflake.com/en/sql-reference/functions/uuid_string
V,,,,,,
VALIDATE,"Validates the files loaded in a past execution of the COPY INTO <table> command and returns all the errors encountered during the load, rather than just the first error.",Table functions,,table,,https://docs.snowflake.com/en/sql-reference/functions/validate
VALIDATE_PIPE_LOAD,This table function can be used to validate data files processed by Snowpipe within a specified time range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/validate_pipe_load
VAR_POP,Returns the population variance of non-NULL records in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/var_pop
VAR_SAMP,Returns the sample variance of non-NULL records in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/var_samp
"VARIANCE , VARIANCE_SAMP",Returns the sample variance of non-NULL records in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/variance
VARIANCE_POP,Returns the population variance of non-NULL records in a group.,"Aggregate functions , Window function syntax and usage",,aggregate,,https://docs.snowflake.com/en/sql-reference/functions/variance_pop
VECTOR_COSINE_SIMILARITY,Computes the cosine similarity between two vectors.,Vector similarity functions,,vector,,https://docs.snowflake.com/en/sql-reference/functions/vector_cosine_similarity
VECTOR_INNER_PRODUCT,Computes the inner product of two vectors.,Vector similarity functions,,vector,,https://docs.snowflake.com/en/sql-reference/functions/vector_inner_product
VECTOR_L1_DISTANCE,Computes the L1 distance between two vectors.,Vector similarity functions,,vector,,https://docs.snowflake.com/en/sql-reference/functions/vector_l1_distance
VECTOR_L2_DISTANCE,Computes the L2 distance between two vectors.,Vector similarity functions,,vector,,https://docs.snowflake.com/en/sql-reference/functions/vector_l2_distance
W,,,,,,
WAREHOUSE_LOAD_HISTORY,This table function can be used to query the activity history (defined as the “query load”) for a single warehouse within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/warehouse_load_history
WAREHOUSE_METERING_HISTORY,This table function can be used in queries to return the hourly credit usage for a single warehouse (or all the warehouses in your account) within a specified date range.,"Information Schema , Table functions",,information_schema,,https://docs.snowflake.com/en/sql-reference/functions/warehouse_metering_history
WIDTH_BUCKET,"Constructs equi-width histograms, in which the histogram range is divided into intervals of identical size, and returns the bucket number into which the value of an expression falls, after it has been evaluated.",Numeric functions,,numeric,,https://docs.snowflake.com/en/sql-reference/functions/width_bucket
X,,,,,,
XMLGET,Extracts an XML element object (often referred to as simply a tag) from the content of the outer XML element based on the name and instance number of the specified tag.,Semi-structured and structured data functions,,semi-structured,"x,l",https://docs.snowflake.com/en/sql-reference/functions/xmlget
Y,,,,,,
YEAR* / DAY* / WEEK* / MONTH / QUARTER,Extracts the corresponding date part from a date or timestamp.,Date & time functions,,datetime,,https://docs.snowflake.com/en/sql-reference/functions/year
Z,,,,,,
ZEROIFNULL,"Returns 0 if its argument is null; otherwise, returns its argument.",Conditional expression functions,,conditional,,https://docs.snowflake.com/en/sql-reference/functions/zeroifnull
ZIPF,"Returns a Zipf-distributed integer, for N elements and characteristic exponent s.",,,generation,,https://docs.snowflake.com/en/sql-reference/functions/zipf