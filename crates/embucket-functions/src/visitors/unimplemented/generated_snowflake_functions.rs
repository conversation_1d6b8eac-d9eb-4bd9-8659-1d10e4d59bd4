// This file is auto-generated by generate_snowflake_functions.py
// Do not edit manually!

use super::FunctionInfo;

pub const ACCOUNT_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("CUMULATIVE_PRIVACY_LOSSES", FunctionInfo::new(
        "CUMULATIVE_PRIVACY_LOSSES",
        "Returns the privacy budgets associated with a specific privacy policy."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/cumulative_privacy_losses")
    ),
    ("ESTIMATE_REMAINING_DP_AGGREGATES", FunctionInfo::new(
        "ESTIMATE_REMAINING_DP_AGGREGATES",
        "Returns the estimated number of aggregation functions that can be run before the limit of a privacy budget is reached."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/estimate_remaining_dp_aggregates")
    ),
    ("TAG_REFERENCES_WITH_LINEAGE", FunctionInfo::new(
        "TAG_REFERENCES_WITH_LINEAGE",
        "Returns a table in which each row displays an association between the specified tag and the Snowflake object to which the tag is associated."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/tag_references_with_lineage")
    ),
];

pub const AGGREGATE_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("APPROXIMATE_JACCARD_INDEX", FunctionInfo::new(
        "APPROXIMATE_JACCARD_INDEX",
        "Returns an estimation of the similarity (Jaccard index) of inputs based on their MinHash states."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approximate_jaccard_index")
    ),
    ("APPROXIMATE_SIMILARITY", FunctionInfo::new(
        "APPROXIMATE_SIMILARITY",
        "Returns an estimation of the similarity (Jaccard index) of inputs based on their MinHash states."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approximate_similarity")
    ),
    ("APPROX_COUNT_DISTINCT", FunctionInfo::new(
        "APPROX_COUNT_DISTINCT",
        "Uses HyperLogLog to return an approximation of the distinct cardinality of the input (i.e. HLL(col1, col2, ... ) returns an approximation of COUNT(DISTINCT col1, col2, ... ))."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_count_distinct")
    ),
    ("APPROX_PERCENTILE", FunctionInfo::new(
        "APPROX_PERCENTILE",
        "Returns an approximated value for the desired percentile (that is, if column c has n numbers, APPROX_PERCENTILE(c, p) returns a number such that approximately n * p of the numbers in c are smaller than the returned number)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_percentile")
    ),
    ("APPROX_PERCENTILE_ACCUMULATE", FunctionInfo::new(
        "APPROX_PERCENTILE_ACCUMULATE",
        "Returns the internal representation of the t-Digest state (as a JSON object) at the end of aggregation."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_percentile_accumulate")
    ),
    ("APPROX_PERCENTILE_COMBINE", FunctionInfo::new(
        "APPROX_PERCENTILE_COMBINE",
        "Combines (merges) percentile input states into a single output state."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_percentile_combine")
    ),
    ("APPROX_PERCENTILE_ESTIMATE", FunctionInfo::new(
        "APPROX_PERCENTILE_ESTIMATE",
        "Returns the desired approximated percentile value for the specified t-Digest state."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_percentile_estimate")
    ),
    ("APPROX_TOP_K", FunctionInfo::new(
        "APPROX_TOP_K",
        "Uses Space-Saving to return an approximation of the most frequent values in the input, along with their approximate frequencies."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_top_k")
    ),
    ("APPROX_TOP_K_ACCUMULATE", FunctionInfo::new(
        "APPROX_TOP_K_ACCUMULATE",
        "Returns the Space-Saving summary at the end of aggregation."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_top_k_accumulate")
    ),
    ("APPROX_TOP_K_COMBINE", FunctionInfo::new(
        "APPROX_TOP_K_COMBINE",
        "Combines (merges) input states into a single output state."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_top_k_combine")
    ),
    ("APPROX_TOP_K_ESTIMATE", FunctionInfo::new(
        "APPROX_TOP_K_ESTIMATE",
        "Returns the approximate most frequent values and their estimated frequency for the given Space-Saving state."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/approx_top_k_estimate")
    ),
    ("BITAND_AGG", FunctionInfo::new(
        "BITAND_AGG",
        "Returns the bitwise AND value of all non-NULL numeric records in a group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitand_agg")
    ),
    ("BITMAP_BIT_POSITION", FunctionInfo::new(
        "BITMAP_BIT_POSITION",
        "Given a numeric value, returns the relative position for the bit that represents that value in a bitmap."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitmap_bit_position")
    ),
    ("BITMAP_BUCKET_NUMBER", FunctionInfo::new(
        "BITMAP_BUCKET_NUMBER",
        "Given a numeric value, returns an identifier (“bucket number”) for the bitmap containing the bit that represents the value.."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitmap_bucket_number")
    ),
    ("BITMAP_CONSTRUCT_AGG", FunctionInfo::new(
        "BITMAP_CONSTRUCT_AGG",
        "Returns a bitmap with bits set for each distinct value in a group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitmap_construct_agg")
    ),
    ("BITMAP_COUNT", FunctionInfo::new(
        "BITMAP_COUNT",
        "Given a bitmap that represents the set of distinct values for a column, returns the number of distinct value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitmap_count")
    ),
    ("BITMAP_OR_AGG", FunctionInfo::new(
        "BITMAP_OR_AGG",
        "Returns a bitmap containing the results of a binary OR operation on the input bitmaps."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitmap_or_agg")
    ),
    ("BITOR_AGG", FunctionInfo::new(
        "BITOR_AGG",
        "Returns the bitwise OR value of all non-NULL numeric records in a group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitor_agg")
    ),
    ("BITXOR_AGG", FunctionInfo::new(
        "BITXOR_AGG",
        "Returns the bitwise XOR value of all non-NULL numeric records in a group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitxor_agg")
    ),
    ("COUNT_IF", FunctionInfo::new(
        "COUNT_IF",
        "Returns the number of records that satisfy a condition or NULL if no records satisfy the condition."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/count_if")
    ),
    ("GROUPING_ID", FunctionInfo::new(
        "GROUPING_ID",
        "Describes which of a list of expressions are grouped in a row produced by a GROUP BY query."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/grouping_id")
    ),
    ("HASH_AGG", FunctionInfo::new(
        "HASH_AGG",
        "Returns an aggregate signed 64-bit hash value over the (unordered) set of input rows."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hash_agg")
    ),
    ("HLL", FunctionInfo::new(
        "HLL",
        "Uses HyperLogLog to return an approximation of the distinct cardinality of the input (i.e. HLL(col1, col2, ... ) returns an approximation of COUNT(DISTINCT col1, col2, ... ))."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hll")
    ),
    ("HLL_ACCUMULATE", FunctionInfo::new(
        "HLL_ACCUMULATE",
        "Returns the HyperLogLog state at the end of aggregation."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hll_accumulate")
    ),
    ("HLL_COMBINE", FunctionInfo::new(
        "HLL_COMBINE",
        "Combines (merges) input states into a single output state."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hll_combine")
    ),
    ("HLL_ESTIMATE", FunctionInfo::new(
        "HLL_ESTIMATE",
        "Returns the cardinality estimate for the given HyperLogLog state."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hll_estimate")
    ),
    ("HLL_EXPORT", FunctionInfo::new(
        "HLL_EXPORT",
        "Converts input in BINARY format to OBJECT format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hll_export")
    ),
    ("HLL_IMPORT", FunctionInfo::new(
        "HLL_IMPORT",
        "Converts input in OBJECT format to BINARY format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hll_import")
    ),
    ("KURTOSIS", FunctionInfo::new(
        "KURTOSIS",
        "Returns the population excess kurtosis of non-NULL records."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/kurtosis")
    ),
    ("MAX_BY", FunctionInfo::new(
        "MAX_BY",
        "Finds the row(s) containing the maximum value for a column and returns the value of another column in that row."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/max_by")
    ),
    ("MINHASH", FunctionInfo::new(
        "MINHASH",
        "Returns a MinHash state containing an array of size k constructed by applying k number of different hash functions to the input rows and keeping the minimum of each hash function."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/minhash")
    ),
    ("MINHASH_COMBINE", FunctionInfo::new(
        "MINHASH_COMBINE",
        "Combines input MinHash states into a single MinHash output state."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/minhash_combine")
    ),
    ("MIN_BY", FunctionInfo::new(
        "MIN_BY",
        "Finds the row(s) containing the minimum value for a column and returns the value of another column in that row."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/min_by")
    ),
    ("MODE", FunctionInfo::new(
        "MODE",
        "Returns the most frequent value for the values within expr1."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/mode")
    ),
    ("PERCENTILE_DISC", FunctionInfo::new(
        "PERCENTILE_DISC",
        "Returns a percentile value based on a discrete distribution of the input column (specified in order_by_expr)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/percentile_disc")
    ),
    ("SKEW", FunctionInfo::new(
        "SKEW",
        "Returns the sample skewness of non-NULL records."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/skew")
    ),
    ("VARIANCE", FunctionInfo::new(
        "VARIANCE",
        "Returns the sample variance of non-NULL records in a group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/variance")
    ),
    ("VARIANCE_POP", FunctionInfo::new(
        "VARIANCE_POP",
        "Returns the population variance of non-NULL records in a group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/variance_pop")
    ),
    ("VARIANCE_SAMP", FunctionInfo::new(
        "VARIANCE_SAMP",
        "Returns the sample variance of non-NULL records in a group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/variance")
    ),
];

pub const BITWISE_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    (
        "BITAND",
        FunctionInfo::new(
            "BITAND",
            "Returns the bitwise AND of two numeric or binary expressions.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitand"),
    ),
    (
        "BITNOT",
        FunctionInfo::new(
            "BITNOT",
            "Returns the bitwise negation of a numeric or binary expression.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitnot"),
    ),
    (
        "BITOR",
        FunctionInfo::new(
            "BITOR",
            "Returns the bitwise OR of two numeric or binary expressions.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitor"),
    ),
    (
        "BITSHIFTLEFT",
        FunctionInfo::new(
            "BITSHIFTLEFT",
            "Shifts the bits for a numeric or binary expression n positions to the left.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitshiftleft"),
    ),
    (
        "BITSHIFTRIGHT",
        FunctionInfo::new(
            "BITSHIFTRIGHT",
            "Shifts the bits for a numeric or binary expression n positions to the right.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitshiftright"),
    ),
    (
        "BITXOR",
        FunctionInfo::new(
            "BITXOR",
            "Returns the bitwise XOR of two numeric or binary expressions.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/bitxor"),
    ),
    (
        "GETBIT",
        FunctionInfo::new(
            "GETBIT",
            "Given an INTEGER value, returns the value of a bit at a specified position.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/getbit"),
    ),
];

pub const CONDITIONAL_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    (
        "BOOLNOT",
        FunctionInfo::new(
            "BOOLNOT",
            "Computes the Boolean NOT of a single numeric expression.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/boolnot"),
    ),
    (
        "CASE",
        FunctionInfo::new("CASE", "Works like a cascading “if-then-else” statement.")
            .with_docs("https://docs.snowflake.com/en/sql-reference/functions/case"),
    ),
    (
        "GREATEST_IGNORE_NULLS",
        FunctionInfo::new(
            "GREATEST_IGNORE_NULLS",
            "Returns the largest non-NULL value from a list of expressions.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/greatest_ignore_nulls"),
    ),
    (
        "LEAST_IGNORE_NULLS",
        FunctionInfo::new(
            "LEAST_IGNORE_NULLS",
            "Returns the smallest non-NULL value from a list of expressions.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/least_ignore_nulls"),
    ),
    (
        "REGR_VALX",
        FunctionInfo::new(
            "REGR_VALX",
            "Returns NULL if the first argument is NULL; otherwise, returns the second argument.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/regr_valx"),
    ),
    (
        "REGR_VALY",
        FunctionInfo::new(
            "REGR_VALY",
            "Returns NULL if the second argument is NULL; otherwise, returns the first argument.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/regr_valy"),
    ),
    (
        "ZEROIFNULL",
        FunctionInfo::new(
            "ZEROIFNULL",
            "Returns 0 if its argument is null; otherwise, returns its argument.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/zeroifnull"),
    ),
];

pub const CONTEXT_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("ALL_USER_NAMES", FunctionInfo::new(
        "ALL_USER_NAMES",
        "Returns all user names in the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/all_user_names")
    ),
    ("CURRENT_ACCOUNT", FunctionInfo::new(
        "CURRENT_ACCOUNT",
        "Returns the account locator used by the user’s current session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_account")
    ),
    ("CURRENT_ACCOUNT_NAME", FunctionInfo::new(
        "CURRENT_ACCOUNT_NAME",
        "Returns the name of the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_account_name")
    ),
    ("CURRENT_AVAILABLE_ROLES", FunctionInfo::new(
        "CURRENT_AVAILABLE_ROLES",
        "Returns a list of all account-level roles granted to the current user."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_available_roles")
    ),
    ("CURRENT_ORGANIZATION_NAME", FunctionInfo::new(
        "CURRENT_ORGANIZATION_NAME",
        "Returns the name of the organization to which the current account belongs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_organization_name")
    ),
    ("CURRENT_REGION", FunctionInfo::new(
        "CURRENT_REGION",
        "Returns the name of the region for the account where the current user is logged in."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_region")
    ),
    ("CURRENT_SECONDARY_ROLES", FunctionInfo::new(
        "CURRENT_SECONDARY_ROLES",
        "Returns the secondary roles in use for the current session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_secondary_roles")
    ),
    ("CURRENT_STATEMENT", FunctionInfo::new(
        "CURRENT_STATEMENT",
        "Returns the SQL text of the statement that is currently executing."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_statement")
    ),
    ("CURRENT_TRANSACTION", FunctionInfo::new(
        "CURRENT_TRANSACTION",
        "Returns the transaction id of an open transaction in the current session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_transaction")
    ),
    ("CURRENT_USER", FunctionInfo::new(
        "CURRENT_USER",
        "Returns the name of the user currently logged into the system."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_user")
    ),
    ("GETDATE", FunctionInfo::new(
        "GETDATE",
        "Returns the current timestamp for the system in the local time zone."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/getdate")
    ),
    ("GETVARIABLE", FunctionInfo::new(
        "GETVARIABLE",
        "Returns the value associated with a SQL variable name."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/getvariable")
    ),
    ("GET_CONDITION_QUERY_UUID", FunctionInfo::new(
        "GET_CONDITION_QUERY_UUID",
        "Returns the query ID for the SQL statement executed for the condition of an alert."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_condition_query_uuid")
    ),
    ("INVOKER_ROLE", FunctionInfo::new(
        "INVOKER_ROLE",
        "Returns the name of the account-level role of the object executing the query or NULL if the name of the role is a database role."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/invoker_role")
    ),
    ("INVOKER_SHARE", FunctionInfo::new(
        "INVOKER_SHARE",
        "Returns the name of the share that directly accessed the table or view where the INVOKER_SHARE function is invoked, otherwise the function returns NULL."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/invoker_share")
    ),
    ("IS_APPLICATION_ROLE_IN_SESSION", FunctionInfo::new(
        "IS_APPLICATION_ROLE_IN_SESSION",
        "Verifies whether the application role is activated in the consumer’s current session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_application_role_in_session")
    ),
    ("IS_DATABASE_ROLE_IN_SESSION", FunctionInfo::new(
        "IS_DATABASE_ROLE_IN_SESSION",
        "Verifies whether the database role is in the user’s active primary or secondary role hierarchy for the current session or if the specified column contains a database role that is in the user’s active primary or secondary role hierarchy for the current session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_database_role_in_session")
    ),
    ("IS_GRANTED_TO_INVOKER_ROLE", FunctionInfo::new(
        "IS_GRANTED_TO_INVOKER_ROLE",
        "Returns TRUE if the role returned by the INVOKER_ROLE function inherits the privileges of the specified role in the argument based on the context in which the function is called."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_granted_to_invoker_role")
    ),
    ("IS_INSTANCE_ROLE_IN_SESSION", FunctionInfo::new(
        "IS_INSTANCE_ROLE_IN_SESSION",
        "Verifies whether the user’s active primary or secondary role hierarchy for the session inherits the specified instance role."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_instance_role_in_session")
    ),
    ("IS_ROLE_IN_SESSION", FunctionInfo::new(
        "IS_ROLE_IN_SESSION",
        "Verifies whether the account role is in the user’s active primary or secondary role hierarchy for the session or if the specified column contains a role that is in the user’s active primary or secondary role hierarchy for the session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_role_in_session")
    ),
    ("LAST_TRANSACTION", FunctionInfo::new(
        "LAST_TRANSACTION",
        "Returns the transaction ID of the last transaction that was either committed or rolled back in the current session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/last_transaction")
    ),
    ("LOCALTIME", FunctionInfo::new(
        "LOCALTIME",
        "Returns the current time for the system."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/localtime")
    ),
    ("LOCALTIMESTAMP", FunctionInfo::new(
        "LOCALTIMESTAMP",
        "Returns the current timestamp for the system in the local time zone."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/localtimestamp")
    ),
    ("POLICY_CONTEXT", FunctionInfo::new(
        "POLICY_CONTEXT",
        "Simulates the results of a query based upon the value of one or more context functions, which lets you determine how policies affect query results."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/policy_context")
    ),
    ("SYSDATE", FunctionInfo::new(
        "SYSDATE",
        "Returns the current timestamp for the system in the UTC time zone."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sysdate")
    ),
    ("SYSTIMESTAMP", FunctionInfo::new(
        "SYSTIMESTAMP",
        "Returns the current timestamp for the system."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/systimestamp")
    ),
];

pub const CONVERSION_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("CAST", FunctionInfo::new(
        "CAST",
        "Converts a value of one data type into another data type."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/cast")
    ),
    ("DATE", FunctionInfo::new(
        "DATE",
        "Converts an input expression to a date."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_date")
    .with_subcategory("datetime")
    ),
    ("ST_GEOGFROMGEOHASH", FunctionInfo::new(
        "ST_GEOGFROMGEOHASH",
        "Returns a GEOGRAPHY object for the polygon that represents the boundaries of a geohash."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geogfromgeohash")
    .with_subcategory("geospatial")
    ),
    ("ST_GEOGPOINTFROMGEOHASH", FunctionInfo::new(
        "ST_GEOGPOINTFROMGEOHASH",
        "Returns a GEOGRAPHY object for the Point that represents the center of a geohash."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geogpointfromgeohash")
    .with_subcategory("geospatial")
    ),
    ("ST_GEOGRAPHYFROMWKB", FunctionInfo::new(
        "ST_GEOGRAPHYFROMWKB",
        "Parses a WKB (well-known binary) or EWKB (extended well-known binary) input and returns a value of type GEOGRAPHY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geographyfromwkb")
    .with_subcategory("geospatial")
    ),
    ("ST_GEOGRAPHYFROMWKT", FunctionInfo::new(
        "ST_GEOGRAPHYFROMWKT",
        "Parses a WKT (well-known text) or EWKT (extended well-known text) input and returns a value of type GEOGRAPHY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geographyfromwkt")
    .with_subcategory("geospatial")
    ),
    ("ST_GEOMETRYFROMWKB", FunctionInfo::new(
        "ST_GEOMETRYFROMWKB",
        "Parses a WKB (well-known binary) or EWKB (extended well-known binary) input and returns a value of type GEOMETRY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geometryfromwkb")
    .with_subcategory("geospatial")
    ),
    ("ST_GEOMETRYFROMWKT", FunctionInfo::new(
        "ST_GEOMETRYFROMWKT",
        "Parses a WKT (well-known text) or EWKT (extended well-known text) input and returns a value of type GEOMETRY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geometryfromwkt")
    .with_subcategory("geospatial")
    ),
    ("TO_ARRAY", FunctionInfo::new(
        "TO_ARRAY",
        "Converts the input expression to an ARRAY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_array")
    .with_subcategory("semi-structured")
    ),
    ("TO_BINARY", FunctionInfo::new(
        "TO_BINARY",
        "Converts the input expression to a binary value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_binary")
    ),
    ("TO_DECIMAL", FunctionInfo::new(
        "TO_DECIMAL",
        "Converts an input expression to a fixed-point number."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_decimal")
    ),
    ("TO_DOUBLE", FunctionInfo::new(
        "TO_DOUBLE",
        "Converts an expression to a double-precision floating-point number."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_double")
    ),
    ("TO_GEOGRAPHY", FunctionInfo::new(
        "TO_GEOGRAPHY",
        "Parses an input and returns a value of type GEOGRAPHY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_geography")
    .with_subcategory("geospatial")
    ),
    ("TO_GEOMETRY", FunctionInfo::new(
        "TO_GEOMETRY",
        "Parses an input and returns a value of type GEOMETRY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_geometry")
    .with_subcategory("geospatial")
    ),
    ("TO_JSON", FunctionInfo::new(
        "TO_JSON",
        "Converts a VARIANT value to a string containing the JSON representation of the value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_json")
    .with_subcategory("semi-structured")
    ),
    ("TO_NUMBER", FunctionInfo::new(
        "TO_NUMBER",
        "Converts an input expression to a fixed-point number."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_decimal")
    ),
    ("TO_NUMERIC", FunctionInfo::new(
        "TO_NUMERIC",
        "Converts an input expression to a fixed-point number."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_decimal")
    ),
    ("TO_OBJECT", FunctionInfo::new(
        "TO_OBJECT",
        "Converts the input value to an OBJECT."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_object")
    .with_subcategory("semi-structured")
    ),
    ("TO_VARCHAR", FunctionInfo::new(
        "TO_VARCHAR",
        "Converts the input expression to a string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_char")
    ),
    ("TO_XML", FunctionInfo::new(
        "TO_XML",
        "Converts a VARIANT to a VARCHAR that contains an XML representation of the value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_xml")
    .with_subcategory("semi-structured")
    ),
    ("TRY_CAST", FunctionInfo::new(
        "TRY_CAST",
        "A special version of CAST , :: that is available for a subset of data type conversions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_cast")
    ),
    ("TRY_TO_BINARY", FunctionInfo::new(
        "TRY_TO_BINARY",
        "A special version of TO_BINARY that performs the same operation (i.e. converts an input expression to a binary value), but with error handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_binary")
    ),
    ("TRY_TO_DATE", FunctionInfo::new(
        "TRY_TO_DATE",
        "A special version of the TO_DATE function that performs the same operation (i.e. converts an input expression to a date), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_date")
    .with_subcategory("datetime")
    ),
    ("TRY_TO_DECIMAL", FunctionInfo::new(
        "TRY_TO_DECIMAL",
        "A special version of TO_DECIMAL , TO_NUMBER , TO_NUMERIC that performs the same operation (i.e. converts an input expression to a fixed-point number), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_decimal")
    ),
    ("TRY_TO_DOUBLE", FunctionInfo::new(
        "TRY_TO_DOUBLE",
        "A special version of TO_DOUBLE that performs the same operation (that is, converts an input expression to a double-precision floating-point number), but with error-handling support (that is, if the conversion can’t be performed, it returns a NULL value instead of raising an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_double")
    ),
    ("TRY_TO_GEOGRAPHY", FunctionInfo::new(
        "TRY_TO_GEOGRAPHY",
        "Parses an input and returns a value of type GEOGRAPHY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_geography")
    .with_subcategory("geospatial")
    ),
    ("TRY_TO_GEOMETRY", FunctionInfo::new(
        "TRY_TO_GEOMETRY",
        "Parses an input and returns a value of type GEOMETRY."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_geometry")
    .with_subcategory("geospatial")
    ),
    ("TRY_TO_NUMBER", FunctionInfo::new(
        "TRY_TO_NUMBER",
        "A special version of TO_DECIMAL , TO_NUMBER , TO_NUMERIC that performs the same operation (i.e. converts an input expression to a fixed-point number), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_decimal")
    ),
    ("TRY_TO_NUMERIC", FunctionInfo::new(
        "TRY_TO_NUMERIC",
        "A special version of TO_DECIMAL , TO_NUMBER , TO_NUMERIC that performs the same operation (i.e. converts an input expression to a fixed-point number), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_decimal")
    ),
    ("TRY_TO_TIMESTAMP", FunctionInfo::new(
        "TRY_TO_TIMESTAMP",
        "Converts an input expression into the corresponding timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_timestamp")
    .with_subcategory("datetime")
    ),
    ("TRY_TO_TIMESTAMP", FunctionInfo::new(
        "TRY_TO_TIMESTAMP",
        "A special version of TO_TIMESTAMP / TO_TIMESTAMP_* that performs the same operation (i.e. converts an input expression into a timestamp), but with error-handling support (i.e. if the conversion cannot be performed, it returns a NULL value instead of raising an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_to_timestamp")
    ),
    ("TRY_TO_TIMESTAMP_LTZ", FunctionInfo::new(
        "TRY_TO_TIMESTAMP_LTZ",
        "Converts an input expression into the corresponding timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_timestamp")
    .with_subcategory("datetime")
    ),
    ("TRY_TO_TIMESTAMP_NTZ", FunctionInfo::new(
        "TRY_TO_TIMESTAMP_NTZ",
        "Converts an input expression into the corresponding timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_timestamp")
    .with_subcategory("datetime")
    ),
    ("TRY_TO_TIMESTAMP_TZ", FunctionInfo::new(
        "TRY_TO_TIMESTAMP_TZ",
        "Converts an input expression into the corresponding timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_timestamp")
    .with_subcategory("datetime")
    ),
];

pub const DATA_METRIC_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("BLANK_COUNT", FunctionInfo::new(
        "BLANK_COUNT",
        "Returns the count of column values that are blank for the specified column in a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_blank_count")
    ),
    ("BLANK_PERCENT", FunctionInfo::new(
        "BLANK_PERCENT",
        "Returns the percentage of column values that are blank for the specified column in a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_blank_percent")
    ),
    ("DATA_METRIC_SCHEDULED_TIME", FunctionInfo::new(
        "DATA_METRIC_SCHEDULED_TIME",
        "Returns the timestamp for when a DMF is scheduled to run or the current timestamp if the function is called manually."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_data_metric_schedule_time")
    ),
    ("DUPLICATE_COUNT", FunctionInfo::new(
        "DUPLICATE_COUNT",
        "Returns the count of column values that have duplicates, including NULL values."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_duplicate_count")
    ),
    ("FRESHNESS", FunctionInfo::new(
        "FRESHNESS",
        "Returns the difference in seconds between the maximum value of a timestamp column and the scheduled time when the data metric function runs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_freshness")
    ),
    ("NULL_COUNT", FunctionInfo::new(
        "NULL_COUNT",
        "Returns the total number of NULL values for the specified columns in a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_null_count")
    ),
    ("NULL_PERCENT", FunctionInfo::new(
        "NULL_PERCENT",
        "Returns the percentage of columns values that are NULL for the specified column in a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_null_percent")
    ),
    ("ROW_COUNT", FunctionInfo::new(
        "ROW_COUNT",
        "Returns the total number of rows for the specified table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_row_count")
    ),
    ("UNIQUE_COUNT", FunctionInfo::new(
        "UNIQUE_COUNT",
        "Returns the total number of unique non-NULL values for the specified columns in a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dmf_unique_count")
    ),
];

pub const DATA_QUALITY_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("DATA_QUALITY_MONITORING_RESULTS", FunctionInfo::new(
        "DATA_QUALITY_MONITORING_RESULTS",
        "Returns a row for each data metric function assigned to the specified object, which includes the evaluation result and other metadata of the data metric function on the object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/data_quality_monitoring_results")
    ),
];

pub const DATETIME_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("DAY", FunctionInfo::new(
        "DAY",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("DAYOFMONTH", FunctionInfo::new(
        "DAYOFMONTH",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("DAYOFWEEK", FunctionInfo::new(
        "DAYOFWEEK",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("DAYOFWEEKISO", FunctionInfo::new(
        "DAYOFWEEKISO",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("DAYOFYEAR", FunctionInfo::new(
        "DAYOFYEAR",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("EXTRACT", FunctionInfo::new(
        "EXTRACT",
        "Extracts the specified date or time part from a date, time, or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/extract")
    ),
    ("HOUR", FunctionInfo::new(
        "HOUR",
        "Extracts the corresponding time part from a time or timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hour-minute-second")
    ),
    ("LAST_SUCCESSFUL_SCHEDULED_TIME", FunctionInfo::new(
        "LAST_SUCCESSFUL_SCHEDULED_TIME",
        "Returns the timestamp representing the scheduled time for the most recent successful evaluation of the alert condition, where no errors occurred when executing the action."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/last_successful_scheduled_time")
    ),
    ("MINUTE", FunctionInfo::new(
        "MINUTE",
        "Extracts the corresponding time part from a time or timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hour-minute-second")
    ),
    ("MONTH", FunctionInfo::new(
        "MONTH",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("MONTHS_BETWEEN", FunctionInfo::new(
        "MONTHS_BETWEEN",
        "Returns the number of months between two DATE or TIMESTAMP values."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/months_between")
    ),
    ("QUARTER", FunctionInfo::new(
        "QUARTER",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("SCHEDULED_TIME", FunctionInfo::new(
        "SCHEDULED_TIME",
        "Returns the timestamp representing the scheduled time of the current alert."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/scheduled_time")
    ),
    ("SECOND", FunctionInfo::new(
        "SECOND",
        "Extracts the corresponding time part from a time or timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hour-minute-second")
    ),
    ("TIME_SLICE", FunctionInfo::new(
        "TIME_SLICE",
        "Calculates the beginning or end of a “slice” of time, where the length of the slice is a multiple of a standard unit of time (minute, hour, day, etc.)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/time_slice")
    ),
    ("WEEK", FunctionInfo::new(
        "WEEK",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("WEEKISO", FunctionInfo::new(
        "WEEKISO",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("WEEKOFYEAR", FunctionInfo::new(
        "WEEKOFYEAR",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("YEAR", FunctionInfo::new(
        "YEAR",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("YEAROFWEEK", FunctionInfo::new(
        "YEAROFWEEK",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
    ("YEAROFWEEKISO", FunctionInfo::new(
        "YEAROFWEEKISO",
        "Extracts the corresponding date part from a date or timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/year")
    ),
];

pub const DIFFERENTIAL_PRIVACY_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("DP_INTERVAL_HIGH", FunctionInfo::new(
        "DP_INTERVAL_HIGH",
        "Returns the upper bound of the noise interval, which is used by differential privacy to help analysts determine how much noise has been introduced into query results."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dp_interval_high")
    ),
    ("DP_INTERVAL_LOW", FunctionInfo::new(
        "DP_INTERVAL_LOW",
        "Returns the lower bound of the noise interval, which is used by differential privacy to help analysts determine how much noise has been introduced into query results."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dp_interval_low")
    ),
];

pub const ENCRYPTION_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("DECRYPT", FunctionInfo::new(
        "DECRYPT",
        "Decrypts a BINARY value using a VARCHAR passphrase."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/decrypt")
    ),
    ("DECRYPT_RAW", FunctionInfo::new(
        "DECRYPT_RAW",
        "Decrypts a BINARY value using a BINARY key."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/decrypt_raw")
    ),
    ("ENCRYPT", FunctionInfo::new(
        "ENCRYPT",
        "Encrypts a VARCHAR or BINARY value using a VARCHAR passphrase."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/encrypt")
    ),
    ("ENCRYPT_RAW", FunctionInfo::new(
        "ENCRYPT_RAW",
        "Encrypts a BINARY value using a BINARY key."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/encrypt_raw")
    ),
    ("TRY_DECRYPT", FunctionInfo::new(
        "TRY_DECRYPT",
        "A special version of DECRYPT that returns a NULL value if an error occurs during decryption."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_decrypt")
    ),
    ("TRY_DECRYPT_RAW", FunctionInfo::new(
        "TRY_DECRYPT_RAW",
        "A special version of DECRYPT_RAW that returns a NULL value if an error occurs during decryption."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_decrypt_raw")
    ),
];

pub const FILE_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("BUILD_SCOPED_FILE_URL", FunctionInfo::new(
        "BUILD_SCOPED_FILE_URL",
        "Generates a scoped Snowflake file URL to a staged file using the stage name and relative file path as inputs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/build_scoped_file_url")
    ),
    ("BUILD_STAGE_FILE_URL", FunctionInfo::new(
        "BUILD_STAGE_FILE_URL",
        "Generates a Snowflake file URL to a staged file using the stage name and relative file path as inputs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/build_stage_file_url")
    ),
    ("GET_ABSOLUTE_PATH", FunctionInfo::new(
        "GET_ABSOLUTE_PATH",
        "Retrieves the absolute path of a staged file using the stage name and path of the file relative to its location in the stage as inputs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_absolute_path")
    ),
    ("GET_PRESIGNED_URL", FunctionInfo::new(
        "GET_PRESIGNED_URL",
        "Generates a pre-signed URL to a file on a stage using the stage name and relative file path as inputs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_presigned_url")
    ),
    ("GET_RELATIVE_PATH", FunctionInfo::new(
        "GET_RELATIVE_PATH",
        "Extracts the path of a staged file relative to its location in the stage using the stage name and absolute file path in cloud storage as inputs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_relative_path")
    ),
    ("GET_STAGE_LOCATION", FunctionInfo::new(
        "GET_STAGE_LOCATION",
        "Retrieves the URL for an external or internal named stage using the stage name as the input."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_stage_location")
    ),
];

pub const GENERATION_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("NORMAL", FunctionInfo::new(
        "NORMAL",
        "Generates a normally-distributed pseudo-random floating point number with specified mean and stddev (standard deviation)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/normal")
    ),
    ("RANDSTR", FunctionInfo::new(
        "RANDSTR",
        "Returns a random string of specified length."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/randstr")
    ),
    ("SEQ1", FunctionInfo::new(
        "SEQ1",
        "Returns a sequence of monotonically increasing integers, with wrap-around."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/seq1")
    ),
    ("SEQ2", FunctionInfo::new(
        "SEQ2",
        "Returns a sequence of monotonically increasing integers, with wrap-around."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/seq1")
    ),
    ("SEQ4", FunctionInfo::new(
        "SEQ4",
        "Returns a sequence of monotonically increasing integers, with wrap-around."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/seq1")
    ),
    ("SEQ8", FunctionInfo::new(
        "SEQ8",
        "Returns a sequence of monotonically increasing integers, with wrap-around."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/seq1")
    ),
    ("UNIFORM", FunctionInfo::new(
        "UNIFORM",
        "Generates a uniformly-distributed pseudo-random number in the inclusive range [min, max]."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/uniform")
    ),
    ("UUID_STRING", FunctionInfo::new(
        "UUID_STRING",
        "Generates either a version 4 (random) or version 5 (named) RFC 4122-compliant universally unique identifier (UUID) as a formatted string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/uuid_string")
    .with_subcategory("string")
    ),
    ("ZIPF", FunctionInfo::new(
        "ZIPF",
        "Returns a Zipf-distributed integer, for N elements and characteristic exponent s."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/zipf")
    ),
];

pub const GEOSPATIAL_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("H3_CELL_TO_BOUNDARY", FunctionInfo::new(
        "H3_CELL_TO_BOUNDARY",
        "Returns the GEOGRAPHY object representing the boundary of an H3 cell."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_boundary")
    ),
    ("H3_CELL_TO_CHILDREN", FunctionInfo::new(
        "H3_CELL_TO_CHILDREN",
        "Returns an array of the INTEGER IDs of the children of an H3 cell for a given resolution."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_children")
    ),
    ("H3_CELL_TO_CHILDREN_STRING", FunctionInfo::new(
        "H3_CELL_TO_CHILDREN_STRING",
        "Returns an array of the VARCHAR values containing the hexadecimal IDs of the children of an H3 cell for a given resolution."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_children_string")
    ),
    ("H3_CELL_TO_PARENT", FunctionInfo::new(
        "H3_CELL_TO_PARENT",
        "Returns the ID of the parent of an H3 cell for a given resolution."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_parent")
    ),
    ("H3_CELL_TO_POINT", FunctionInfo::new(
        "H3_CELL_TO_POINT",
        "Returns the GEOGRAPHY object representing the Point that is the centroid of an H3 cell."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_cell_to_point")
    ),
    ("H3_COMPACT_CELLS", FunctionInfo::new(
        "H3_COMPACT_CELLS",
        "Returns an array of VARIANT values that contain the INTEGER IDs of fewer, larger H3 cells that cover the same area as the H3 cells in the input."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_compact_cells")
    ),
    ("H3_COMPACT_CELLS_STRINGS", FunctionInfo::new(
        "H3_COMPACT_CELLS_STRINGS",
        "Returns an array of VARIANT values that contain the VARCHAR hexadecimal IDs of fewer, larger H3 cells that cover the same area as the H3 cells in the input."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_compact_cells_strings")
    ),
    ("H3_COVERAGE", FunctionInfo::new(
        "H3_COVERAGE",
        "Returns an array of IDs (as INTEGER values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_coverage")
    ),
    ("H3_COVERAGE_STRINGS", FunctionInfo::new(
        "H3_COVERAGE_STRINGS",
        "Returns an array of hexadecimal IDs (as VARCHAR values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_coverage_strings")
    ),
    ("H3_GET_RESOLUTION", FunctionInfo::new(
        "H3_GET_RESOLUTION",
        "Returns the resolution of an H3 cell."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_get_resolution")
    ),
    ("H3_GRID_DISK", FunctionInfo::new(
        "H3_GRID_DISK",
        "Returns an array of the IDs of the H3 cells that are within the k-distance from the specified cell."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_grid_disk")
    ),
    ("H3_GRID_DISTANCE", FunctionInfo::new(
        "H3_GRID_DISTANCE",
        "Returns the distance between two H3 cells specified by their IDs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_grid_distance")
    ),
    ("H3_GRID_PATH", FunctionInfo::new(
        "H3_GRID_PATH",
        "Returns an array of the IDs of the H3 cells that represent the line between two cells."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_grid_path")
    ),
    ("H3_INT_TO_STRING", FunctionInfo::new(
        "H3_INT_TO_STRING",
        "Converts the INTEGER value of an H3 cell ID to hexadecimal format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_int_to_string")
    ),
    ("H3_IS_PENTAGON", FunctionInfo::new(
        "H3_IS_PENTAGON",
        "Returns TRUE if the boundary of an H3 cell represents a pentagon."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_is_pentagon")
    ),
    ("H3_IS_VALID_CELL", FunctionInfo::new(
        "H3_IS_VALID_CELL",
        "Returns TRUE if the input represents a valid H3 cell."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_is_valid_cell")
    ),
    ("H3_LATLNG_TO_CELL", FunctionInfo::new(
        "H3_LATLNG_TO_CELL",
        "Returns the INTEGER value of the H3 cell ID for a given latitude, longitude, and resolution."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_latlng_to_cell")
    ),
    ("H3_LATLNG_TO_CELL_STRING", FunctionInfo::new(
        "H3_LATLNG_TO_CELL_STRING",
        "Returns the H3 cell ID in hexadecimal format (as a VARCHAR value) for a given latitude, longitude, and resolution."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_latlng_to_cell_string")
    ),
    ("H3_POINT_TO_CELL", FunctionInfo::new(
        "H3_POINT_TO_CELL",
        "Returns the INTEGER value of an H3 cell ID for a Point (specified by a GEOGRAPHY object) at a given resolution."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_point_to_cell")
    ),
    ("H3_POINT_TO_CELL_STRING", FunctionInfo::new(
        "H3_POINT_TO_CELL_STRING",
        "Returns the hexadecimal value of an H3 cell ID for a Point (specified by a GEOGRAPHY object) at a given resolution."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_point_to_cell_string")
    ),
    ("H3_POLYGON_TO_CELLS", FunctionInfo::new(
        "H3_POLYGON_TO_CELLS",
        "Returns an array of INTEGER values of the IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_polygon_to_cells")
    ),
    ("H3_POLYGON_TO_CELLS_STRINGS", FunctionInfo::new(
        "H3_POLYGON_TO_CELLS_STRINGS",
        "Returns an array of VARCHAR values of the hexadecimal IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_polygon_to_cells_strings")
    ),
    ("H3_STRING_TO_INT", FunctionInfo::new(
        "H3_STRING_TO_INT",
        "Converts an H3 cell ID in hexadecimal format to an INTEGER value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_string_to_int")
    ),
    ("H3_TRY_COVERAGE", FunctionInfo::new(
        "H3_TRY_COVERAGE",
        "A special version of H3_COVERAGE that returns NULL if an error occurs when it attempts to return an array of IDs (as INTEGER values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_try_coverage")
    ),
    ("H3_TRY_COVERAGE_STRINGS", FunctionInfo::new(
        "H3_TRY_COVERAGE_STRINGS",
        "A special version of H3_COVERAGE_STRINGS that returns NULL if an error occurs when it attempts to return an array of hexadecimal IDs (as VARCHAR values) identifying the minimal set of H3 cells that completely cover a shape (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_try_coverage_strings")
    ),
    ("H3_TRY_GRID_DISTANCE", FunctionInfo::new(
        "H3_TRY_GRID_DISTANCE",
        "A special version of H3_GRID_DISTANCE that returns NULL if an error occurs when it attempts to return the distance between two H3 cells."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_try_grid_distance")
    ),
    ("H3_TRY_GRID_PATH", FunctionInfo::new(
        "H3_TRY_GRID_PATH",
        "A special version of H3_GRID_PATH that returns NULL if an error occurs when it attempts to return an array of VARIANT values that contain the IDs of the H3 cells that represent the line between two cells."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_try_grid_path")
    ),
    ("H3_TRY_POLYGON_TO_CELLS", FunctionInfo::new(
        "H3_TRY_POLYGON_TO_CELLS",
        "A special version of H3_POLYGON_TO_CELLS that returns NULL if an error occurs when it attempts to return an array of INTEGER values of the IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_try_polygon_to_cells")
    ),
    ("H3_TRY_POLYGON_TO_CELLS_STRINGS", FunctionInfo::new(
        "H3_TRY_POLYGON_TO_CELLS_STRINGS",
        "A special version of H3_POLYGON_TO_CELLS_STRINGS that returns NULL if an error occurs when it attempts to return an array of VARCHAR values of the hexadecimal IDs of H3 cells that have centroids contained by a Polygon (specified by a GEOGRAPHY object)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_try_polygon_to_cells_strings")
    ),
    ("H3_UNCOMPACT_CELLS", FunctionInfo::new(
        "H3_UNCOMPACT_CELLS",
        "Returns an array of VARIANT values that contain the INTEGER IDs of H3 cells at the specified resolution that cover the same area as the H3 cells in the input."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_uncompact_cells")
    ),
    ("H3_UNCOMPACT_CELLS_STRINGS", FunctionInfo::new(
        "H3_UNCOMPACT_CELLS_STRINGS",
        "Returns an array of VARIANT values that contain the VARCHAR hexadecimal IDs of H3 cells at the specified resolution that cover the same area as the H3 cells in the input."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/h3_uncompact_cells_strings")
    ),
    ("HAVERSINE", FunctionInfo::new(
        "HAVERSINE",
        "Calculates the great-circle distance in kilometers between two points on the Earth’s surface, using the Haversine formula."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/haversine")
    ),
    ("ST_AREA", FunctionInfo::new(
        "ST_AREA",
        "Returns the area of the Polygon(s) in a GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_area")
    ),
    ("ST_ASBINARY", FunctionInfo::new(
        "ST_ASBINARY",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the binary representation of that value in WKB (well-known binary) format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_aswkb")
    ),
    ("ST_ASEWKB", FunctionInfo::new(
        "ST_ASEWKB",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the binary representation of that value in EWKB (extended well-known binary) format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_asewkb")
    ),
    ("ST_ASEWKT", FunctionInfo::new(
        "ST_ASEWKT",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the text (VARCHAR) representation of that value in EWKT (extended well-known text) format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_asewkt")
    ),
    ("ST_ASGEOJSON", FunctionInfo::new(
        "ST_ASGEOJSON",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the GeoJSON representation of that value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_asgeojson")
    ),
    ("ST_ASTEXT", FunctionInfo::new(
        "ST_ASTEXT",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the text (VARCHAR) representation of that value in WKT (well-known text) format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_aswkt")
    ),
    ("ST_ASWKB", FunctionInfo::new(
        "ST_ASWKB",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the binary representation of that value in WKB (well-known binary) format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_aswkb")
    ),
    ("ST_ASWKT", FunctionInfo::new(
        "ST_ASWKT",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the text (VARCHAR) representation of that value in WKT (well-known text) format."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_aswkt")
    ),
    ("ST_AZIMUTH", FunctionInfo::new(
        "ST_AZIMUTH",
        "Given a Point that represents the origin (the location of the observer) and a specified Point, returns the azimuth in radians."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_azimuth")
    ),
    ("ST_BUFFER", FunctionInfo::new(
        "ST_BUFFER",
        "Returns a GEOMETRY object that represents a MultiPolygon containing the points within a specified distance of the input GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_buffer")
    ),
    ("ST_CENTROID", FunctionInfo::new(
        "ST_CENTROID",
        "Returns the Point representing the geometric center of a GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_centroid")
    ),
    ("ST_COLLECT", FunctionInfo::new(
        "ST_COLLECT",
        "There are two forms of ST_COLLECT."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_collect")
    ),
    ("ST_CONTAINS", FunctionInfo::new(
        "ST_CONTAINS",
        "Returns TRUE if a GEOGRAPHY or GEOMETRY object is completely inside another object of the same type."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_contains")
    ),
    ("ST_COVEREDBY", FunctionInfo::new(
        "ST_COVEREDBY",
        "Returns TRUE if no point in one geospatial object is outside another geospatial object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_coveredby")
    ),
    ("ST_COVERS", FunctionInfo::new(
        "ST_COVERS",
        "Returns TRUE if no point in one geospatial object is outside of another geospatial object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_covers")
    ),
    ("ST_DIFFERENCE", FunctionInfo::new(
        "ST_DIFFERENCE",
        "Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the points in the first object that are not in the second object (i.e. the difference between the two objects)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_difference")
    ),
    ("ST_DIMENSION", FunctionInfo::new(
        "ST_DIMENSION",
        "Given a value of type GEOGRAPHY or GEOMETRY, return the “dimension” of the value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_dimension")
    ),
    ("ST_DISJOINT", FunctionInfo::new(
        "ST_DISJOINT",
        "Returns TRUE if the two GEOGRAPHY objects or the two GEOMETRY objects are disjoint (i.e. do not share any portion of space)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_disjoint")
    ),
    ("ST_DISTANCE", FunctionInfo::new(
        "ST_DISTANCE",
        "Returns the minimum geodesic distance between two GEOGRAPHY or the minimum Euclidean distance between two GEOMETRY objects."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_distance")
    ),
    ("ST_DWITHIN", FunctionInfo::new(
        "ST_DWITHIN",
        "Returns TRUE if the minimum geodesic distance between two points (two GEOGRAPHY objects) is within the specified distance."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_dwithin")
    ),
    ("ST_ENDPOINT", FunctionInfo::new(
        "ST_ENDPOINT",
        "Returns the last Point in a LineString."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_endpoint")
    ),
    ("ST_ENVELOPE", FunctionInfo::new(
        "ST_ENVELOPE",
        "Returns the minimum bounding box (a rectangular “envelope”) that encloses a specified GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_envelope")
    ),
    ("ST_GEOHASH", FunctionInfo::new(
        "ST_GEOHASH",
        "Returns the geohash for a GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geohash")
    ),
    ("ST_GEOMFROMGEOHASH", FunctionInfo::new(
        "ST_GEOMFROMGEOHASH",
        "Returns a GEOMETRY object for the polygon that represents the boundaries of a geohash."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geomfromgeohash")
    ),
    ("ST_GEOMPOINT", FunctionInfo::new(
        "ST_GEOMPOINT",
        "Constructs a GEOMETRY object that represents a Point with the specified longitude and latitude."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makegeompoint")
    ),
    ("ST_GEOMPOINTFROMGEOHASH", FunctionInfo::new(
        "ST_GEOMPOINTFROMGEOHASH",
        "Returns a GEOMETRY object for the point that represents center of a geohash."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_geompointfromgeohash")
    ),
    ("ST_HAUSDORFFDISTANCE", FunctionInfo::new(
        "ST_HAUSDORFFDISTANCE",
        "Returns the discrete Hausdorff distance between two GEOGRAPHY objects."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_hausdorffdistance")
    ),
    ("ST_INTERPOLATE", FunctionInfo::new(
        "ST_INTERPOLATE",
        "Given an input GEOGRAPHY object, returns an interpolated object that is within a specified tolerance."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_interpolate")
    ),
    ("ST_INTERSECTION", FunctionInfo::new(
        "ST_INTERSECTION",
        "Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the shape containing the set of points that are common to both input objects (i.e. the intersection of the two objects)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_intersection")
    ),
    ("ST_INTERSECTION_AGG", FunctionInfo::new(
        "ST_INTERSECTION_AGG",
        "Given a GEOGRAPHY column, returns a GEOGRAPHY object that represents the shape containing the combined set of points that are common to the shapes represented by the objects in the column (that is, the intersection of the shapes)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_intersection_agg")
    ),
    ("ST_INTERSECTS", FunctionInfo::new(
        "ST_INTERSECTS",
        "Returns TRUE if the two GEOGRAPHY objects or the two GEOMETRY objects intersect (i.e. share any portion of space)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_intersects")
    ),
    ("ST_ISVALID", FunctionInfo::new(
        "ST_ISVALID",
        "Returns TRUE if the specified GEOGRAPHY or GEOMETRY object represents a valid shape."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_isvalid")
    ),
    ("ST_LENGTH", FunctionInfo::new(
        "ST_LENGTH",
        "Returns the geodesic length of the LineString(s) in a GEOGRAPHY object or the Euclidean length of the LineString(s) in a GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_length")
    ),
    ("ST_MAKEGEOMPOINT", FunctionInfo::new(
        "ST_MAKEGEOMPOINT",
        "Constructs a GEOMETRY object that represents a Point with the specified longitude and latitude."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makegeompoint")
    ),
    ("ST_MAKELINE", FunctionInfo::new(
        "ST_MAKELINE",
        "Constructs a GEOGRAPHY or GEOMETRY object that represents a line connecting the points in the input objects."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makeline")
    ),
    ("ST_MAKEPOINT", FunctionInfo::new(
        "ST_MAKEPOINT",
        "Constructs a GEOGRAPHY object that represents a point with the specified longitude and latitude."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makepoint")
    ),
    ("ST_MAKEPOLYGON", FunctionInfo::new(
        "ST_MAKEPOLYGON",
        "Constructs a GEOGRAPHY or GEOMETRY object that represents a Polygon without holes."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makepolygon")
    ),
    ("ST_MAKEPOLYGONORIENTED", FunctionInfo::new(
        "ST_MAKEPOLYGONORIENTED",
        "Constructs a GEOGRAPHY object that represents a Polygon without holes."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makepolygonoriented")
    ),
    ("ST_NPOINTS", FunctionInfo::new(
        "ST_NPOINTS",
        "Returns the number of points in a GEOGRAPHY or GEOGRAPHY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_npoints")
    ),
    ("ST_NUMPOINTS", FunctionInfo::new(
        "ST_NUMPOINTS",
        "Returns the number of points in a GEOGRAPHY or GEOGRAPHY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_npoints")
    ),
    ("ST_PERIMETER", FunctionInfo::new(
        "ST_PERIMETER",
        "Returns the length of the perimeter of the polygon(s) in a GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_perimeter")
    ),
    ("ST_POINT", FunctionInfo::new(
        "ST_POINT",
        "Constructs a GEOGRAPHY object that represents a point with the specified longitude and latitude."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makepoint")
    ),
    ("ST_POINTN", FunctionInfo::new(
        "ST_POINTN",
        "Returns a Point at a specified index in a LineString."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_pointn")
    ),
    ("ST_POLYGON", FunctionInfo::new(
        "ST_POLYGON",
        "Constructs a GEOGRAPHY or GEOMETRY object that represents a Polygon without holes."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_makepolygon")
    ),
    ("ST_SETSRID", FunctionInfo::new(
        "ST_SETSRID",
        "Returns a GEOMETRY object that has its SRID (spatial reference system identifier) set to the specified value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_setsrid")
    ),
    ("ST_SIMPLIFY", FunctionInfo::new(
        "ST_SIMPLIFY",
        "Given an input GEOGRAPHY or GEOMETRY object that represents a Line or Polygon, returns a simpler approximation of the object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_simplify")
    ),
    ("ST_SRID", FunctionInfo::new(
        "ST_SRID",
        "Returns the SRID (spatial reference system identifier) of a GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_srid")
    ),
    ("ST_STARTPOINT", FunctionInfo::new(
        "ST_STARTPOINT",
        "Returns the first Point in a LineString."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_startpoint")
    ),
    ("ST_SYMDIFFERENCE", FunctionInfo::new(
        "ST_SYMDIFFERENCE",
        "Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the set of points from both input objects that are not part of the intersection of the objects (i.e. the symmetric difference of the two objects)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_symdifference")
    ),
    ("ST_TRANSFORM", FunctionInfo::new(
        "ST_TRANSFORM",
        "Converts a GEOMETRY object from one spatial reference system (SRS) to another."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_transform")
    ),
    ("ST_UNION", FunctionInfo::new(
        "ST_UNION",
        "Given two input GEOGRAPHY objects, returns a GEOGRAPHY object that represents the combined set of shapes for both objects (i.e. the union of the two shapes)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_union")
    ),
    ("ST_UNION_AGG", FunctionInfo::new(
        "ST_UNION_AGG",
        "Given a GEOGRAPHY column, returns a GEOGRAPHY object that represents the combined set of points that are in at least one of the shapes represented by the objects in the column (that is, the union of the shapes)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_union_agg")
    ),
    ("ST_WITHIN", FunctionInfo::new(
        "ST_WITHIN",
        "Returns true if the first geospatial object is fully contained by the second geospatial object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_within")
    ),
    ("ST_X", FunctionInfo::new(
        "ST_X",
        "Returns the longitude (X coordinate) of a Point represented by a GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_x")
    ),
    ("ST_XMAX", FunctionInfo::new(
        "ST_XMAX",
        "Returns the maximum longitude (X coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_xmax")
    ),
    ("ST_XMIN", FunctionInfo::new(
        "ST_XMIN",
        "Returns the minimum longitude (X coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_xmin")
    ),
    ("ST_Y", FunctionInfo::new(
        "ST_Y",
        "Returns the latitude (Y coordinate) of a Point represented by a GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_y")
    ),
    ("ST_YMAX", FunctionInfo::new(
        "ST_YMAX",
        "Returns the maximum latitude (Y coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_ymax")
    ),
    ("ST_YMIN", FunctionInfo::new(
        "ST_YMIN",
        "Returns the minimum latitude (Y coordinate) of all points contained in the specified GEOGRAPHY or GEOMETRY object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/st_ymin")
    ),
];

pub const HASH_FUNCTIONS: &[(&str, FunctionInfo)] = &[(
    "HASH",
    FunctionInfo::new("HASH", "Returns a signed 64-bit hash value.")
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hash"),
)];

pub const ICEBERG_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("ICEBERG_TABLE_FILES", FunctionInfo::new(
        "ICEBERG_TABLE_FILES",
        "Returns information about the data files registered to an externally managed"
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/iceberg_table_files")
    ),
    ("ICEBERG_TABLE_SNAPSHOT_REFRESH_HISTORY", FunctionInfo::new(
        "ICEBERG_TABLE_SNAPSHOT_REFRESH_HISTORY",
        "Returns metadata and snapshot information about the most recent refresh history for a specified externally managed"
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/iceberg_table_snapshot_refresh_history")
    ),
];

pub const INFORMATION_SCHEMA_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("ALERT_HISTORY", FunctionInfo::new(
        "ALERT_HISTORY",
        "This INFORMATION_SCHEMA table function can be used to query the history of alerts within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/alert_history")
    ),
    ("AUTOMATIC_CLUSTERING_HISTORY", FunctionInfo::new(
        "AUTOMATIC_CLUSTERING_HISTORY",
        "This table function is used for querying the Automatic Clustering history for given tables within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/automatic_clustering_history")
    ),
    ("AUTO_REFRESH_REGISTRATION_HISTORY", FunctionInfo::new(
        "AUTO_REFRESH_REGISTRATION_HISTORY",
        "This table function can be used to query the history of data files registered in the metadata of specified objects and the credits billed for these operations."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/auto_refresh_registration_history")
    ),
    ("AVAILABLE_LISTING_REFRESH_HISTORY", FunctionInfo::new(
        "AVAILABLE_LISTING_REFRESH_HISTORY",
        "Returns the past 14 days of refresh history for an available listing or a database mounted from a listing using cross-cloud auto-fulfillment."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/available_listing_refresh_history")
    ),
    ("COMPLETE_TASK_GRAPHS", FunctionInfo::new(
        "COMPLETE_TASK_GRAPHS",
        "Returns the status of a completed graph run."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/complete_task_graphs")
    ),
    ("COPY_HISTORY", FunctionInfo::new(
        "COPY_HISTORY",
        "This table function can be used to query Snowflake data loading history along various dimensions within the last 14 days."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/copy_history")
    ),
    ("CURRENT_TASK_GRAPHS", FunctionInfo::new(
        "CURRENT_TASK_GRAPHS",
        "Returns the status of a graph run that is currently scheduled or is executing."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/current_task_graphs")
    ),
    ("DATABASE_REFRESH_HISTORY", FunctionInfo::new(
        "DATABASE_REFRESH_HISTORY",
        "Returns the refresh history for a secondary database."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/database_refresh_history")
    ),
    ("DATABASE_REFRESH_PROGRESS", FunctionInfo::new(
        "DATABASE_REFRESH_PROGRESS",
        "The DATABASE_REFRESH_PROGRESS family of functions can be used to query the status of a database refresh along various dimensions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/database_refresh_progress")
    ),
    ("DATABASE_REFRESH_PROGRESS_BY_JOB", FunctionInfo::new(
        "DATABASE_REFRESH_PROGRESS_BY_JOB",
        "The DATABASE_REFRESH_PROGRESS family of functions can be used to query the status of a database refresh along various dimensions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/database_refresh_progress")
    ),
    ("DATABASE_REPLICATION_USAGE_HISTORY", FunctionInfo::new(
        "DATABASE_REPLICATION_USAGE_HISTORY",
        "This table function can be used to query the replication history for a specified database within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/database_replication_usage_history")
    ),
    ("DATABASE_STORAGE_USAGE_HISTORY", FunctionInfo::new(
        "DATABASE_STORAGE_USAGE_HISTORY",
        "This table function can be used to query the average daily storage usage, in bytes, for a single database (or all the databases in your account) within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/database_storage_usage_history")
    ),
    ("DATA_METRIC_FUNCTION_REFERENCES", FunctionInfo::new(
        "DATA_METRIC_FUNCTION_REFERENCES",
        "Returns a row for each object that has the specified data metric function assigned to the object or returns a row for each data metric function assigned to the specified object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/data_metric_function_references")
    ),
    ("DATA_TRANSFER_HISTORY", FunctionInfo::new(
        "DATA_TRANSFER_HISTORY",
        "This table function can be used to query the history of data transferred from Snowflake tables into a different cloud storage provider’s network (i.e. from Snowflake on AWS, Google Cloud Platform, or Microsoft Azure into the other cloud provider’s network) and/or geographical region within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/data_transfer_history")
    ),
    ("DYNAMIC_TABLES", FunctionInfo::new(
        "DYNAMIC_TABLES",
        "This table function returns metadata about dynamic tables, including aggregate lag metrics and the status of the most recent refreshes, within 7 days of the current time."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dynamic_tables")
    ),
    ("DYNAMIC_TABLE_GRAPH_HISTORY", FunctionInfo::new(
        "DYNAMIC_TABLE_GRAPH_HISTORY",
        "This table function returns information on all dynamic tables in the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dynamic_table_graph_history")
    ),
    ("DYNAMIC_TABLE_REFRESH_HISTORY", FunctionInfo::new(
        "DYNAMIC_TABLE_REFRESH_HISTORY",
        "This table function returns information about each refresh (completed and running) of dynamic tables."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/dynamic_table_refresh_history")
    ),
    ("EXTERNAL_FUNCTIONS_HISTORY", FunctionInfo::new(
        "EXTERNAL_FUNCTIONS_HISTORY",
        "This table function retrieves the history of external functions called by Snowflake for your entire Snowflake account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/external_functions_history")
    ),
    ("EXTERNAL_TABLE_FILES", FunctionInfo::new(
        "EXTERNAL_TABLE_FILES",
        "This table function can be used to query information about the staged data files included in the metadata for a specified external table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/external_table_files")
    ),
    ("EXTERNAL_TABLE_FILE_REGISTRATION_HISTORY", FunctionInfo::new(
        "EXTERNAL_TABLE_FILE_REGISTRATION_HISTORY",
        "This table function can be used to query information about the metadata history for an external table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/external_table_registration_history")
    ),
    ("LISTING_REFRESH_HISTORY", FunctionInfo::new(
        "LISTING_REFRESH_HISTORY",
        "Returns the past 14 days of refresh history for a cross-cloud auto-fulfillment listing."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/listing_refresh_history")
    ),
    ("LOGIN_HISTORY", FunctionInfo::new(
        "LOGIN_HISTORY",
        "The LOGIN_HISTORY family of table functions can be used to query login attempts by Snowflake users along various dimensions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/login_history")
    ),
    ("LOGIN_HISTORY_BY_USER", FunctionInfo::new(
        "LOGIN_HISTORY_BY_USER",
        "The LOGIN_HISTORY family of table functions can be used to query login attempts by Snowflake users along various dimensions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/login_history")
    ),
    ("MATERIALIZED_VIEW_REFRESH_HISTORY", FunctionInfo::new(
        "MATERIALIZED_VIEW_REFRESH_HISTORY",
        "This table function is used for querying the materialized views refresh history for a specified materialized view within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/materialized_view_refresh_history")
    ),
    ("NETWORK_RULE_REFERENCES", FunctionInfo::new(
        "NETWORK_RULE_REFERENCES",
        "Returns a row for each object with which the specified network rule is associated or returns a row for each network rule associated with the specified container."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/network_rule_references")
    ),
    ("NOTIFICATION_HISTORY", FunctionInfo::new(
        "NOTIFICATION_HISTORY",
        "This table function can be used to query the history of notifications sent through Snowflake."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/notification_history")
    ),
    ("PIPE_USAGE_HISTORY", FunctionInfo::new(
        "PIPE_USAGE_HISTORY",
        "This table function can be used to query the history of data loaded into Snowflake tables using Snowpipe within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/pipe_usage_history")
    ),
    ("POLICY_REFERENCES", FunctionInfo::new(
        "POLICY_REFERENCES",
        "Returns a row for each object that has the specified policy assigned to the object or returns a row for each policy assigned to the specified object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/policy_references")
    ),
    ("QUERY_ACCELERATION_HISTORY", FunctionInfo::new(
        "QUERY_ACCELERATION_HISTORY",
        "The QUERY_ACCELERATION_HISTORY function is used for querying the query acceleration service history within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/query_acceleration_history")
    ),
    ("QUERY_HISTORY", FunctionInfo::new(
        "QUERY_HISTORY",
        "The QUERY_HISTORY family of table functions can be used to query Snowflake query history along various dimensions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/query_history")
    ),
    ("REPLICATION_GROUP_REFRESH_HISTORY", FunctionInfo::new(
        "REPLICATION_GROUP_REFRESH_HISTORY",
        "Returns the replication history for a secondary replication or failover group within the last 14 days."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/replication_group_refresh_history")
    ),
    ("REPLICATION_GROUP_REFRESH_PROGRESS", FunctionInfo::new(
        "REPLICATION_GROUP_REFRESH_PROGRESS",
        "The REPLICATION_GROUP_REFRESH_PROGRESS family of functions can be used to query the status of a replication or failover group refresh."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/replication_group_refresh_progress")
    ),
    ("REPLICATION_GROUP_REFRESH_PROGRESS_BY_JOB", FunctionInfo::new(
        "REPLICATION_GROUP_REFRESH_PROGRESS_BY_JOB",
        "The REPLICATION_GROUP_REFRESH_PROGRESS family of functions can be used to query the status of a replication or failover group refresh."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/replication_group_refresh_progress")
    ),
    ("REPLICATION_GROUP_USAGE_HISTORY", FunctionInfo::new(
        "REPLICATION_GROUP_USAGE_HISTORY",
        "Returns the replication usage history for secondary replication or failover groups within the last 14 days."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/replication_group_usage_history")
    ),
    ("REPLICATION_USAGE_HISTORY", FunctionInfo::new(
        "REPLICATION_USAGE_HISTORY",
        "This table function can be used to query the replication history for a specified database within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/replication_usage_history")
    ),
    ("SEARCH_OPTIMIZATION_HISTORY", FunctionInfo::new(
        "SEARCH_OPTIMIZATION_HISTORY",
        "This table function is used for querying the search optimization service maintenance history for a specified table within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/search_optimization_history")
    ),
    ("SERVERLESS_ALERT_HISTORY", FunctionInfo::new(
        "SERVERLESS_ALERT_HISTORY",
        "This table function is used for querying the serverless alert usage history."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/serverless_alert_history")
    ),
    ("SERVERLESS_TASK_HISTORY", FunctionInfo::new(
        "SERVERLESS_TASK_HISTORY",
        "This table function is used for querying the serverless task usage history."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/serverless_task_history")
    ),
    ("STAGE_DIRECTORY_FILE_REGISTRATION_HISTORY", FunctionInfo::new(
        "STAGE_DIRECTORY_FILE_REGISTRATION_HISTORY",
        "This table function can be used to query information about the metadata history for a directory table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/stage_directory_file_registration_history")
    ),
    ("STAGE_STORAGE_USAGE_HISTORY", FunctionInfo::new(
        "STAGE_STORAGE_USAGE_HISTORY",
        "This table function can be used to query the average daily data storage usage, in bytes, for all the Snowflake stages in your account within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/stage_storage_usage_history")
    ),
    ("TAG_REFERENCES", FunctionInfo::new(
        "TAG_REFERENCES",
        "Returns a table in which each row displays an association between a tag and value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/tag_references")
    ),
    ("TAG_REFERENCES_ALL_COLUMNS", FunctionInfo::new(
        "TAG_REFERENCES_ALL_COLUMNS",
        "Returns a table in which each row displays the tag name and tag value assigned to a specific column."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/tag_references_all_columns")
    ),
    ("TASK_DEPENDENTS", FunctionInfo::new(
        "TASK_DEPENDENTS",
        "This table function returns the list of child tasks for a given root task in a task graph."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/task_dependents")
    ),
    ("TASK_HISTORY", FunctionInfo::new(
        "TASK_HISTORY",
        "You can use this table function to query the history of task usage within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/task_history")
    ),
    ("VALIDATE_PIPE_LOAD", FunctionInfo::new(
        "VALIDATE_PIPE_LOAD",
        "This table function can be used to validate data files processed by Snowpipe within a specified time range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/validate_pipe_load")
    ),
    ("WAREHOUSE_LOAD_HISTORY", FunctionInfo::new(
        "WAREHOUSE_LOAD_HISTORY",
        "This table function can be used to query the activity history (defined as the “query load”) for a single warehouse within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/warehouse_load_history")
    ),
    ("WAREHOUSE_METERING_HISTORY", FunctionInfo::new(
        "WAREHOUSE_METERING_HISTORY",
        "This table function can be used in queries to return the hourly credit usage for a single warehouse (or all the warehouses in your account) within a specified date range."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/warehouse_metering_history")
    ),
];

pub const METADATA_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("GENERATE_COLUMN_DESCRIPTION", FunctionInfo::new(
        "GENERATE_COLUMN_DESCRIPTION",
        "Generates a list of columns from a set of staged files that contain semi-structured data using the INFER_SCHEMA function output."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/generate_column_description")
    ),
    ("GET_DDL", FunctionInfo::new(
        "GET_DDL",
        "Returns a DDL statement that can be used to recreate the specified object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_ddl")
    ),
];

pub const NOTIFICATION_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("APPLICATION_JSON", FunctionInfo::new(
        "APPLICATION_JSON",
        "Returns a JSON object that specifies the JSON message to use for a notification."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/application_json")
    ),
    ("EMAIL_INTEGRATION_CONFIG", FunctionInfo::new(
        "EMAIL_INTEGRATION_CONFIG",
        "Returns a JSON object that specifies the email notification integration, recipients, and subject line to use for an email notification."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/email_integration_config")
    ),
    ("INTEGRATION", FunctionInfo::new(
        "INTEGRATION",
        "Returns a JSON object that specifies the notification integration to use to send a message."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/integration")
    ),
    ("SANITIZE_WEBHOOK_CONTENT", FunctionInfo::new(
        "SANITIZE_WEBHOOK_CONTENT",
        "Removes placeholders (for example, the SNOWFLAKE_WEBHOOK_SECRET placeholder, which specifies a secret) from the body of a notification message to be sent."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sanitize_webhook_content")
    ),
    ("TEXT_HTML", FunctionInfo::new(
        "TEXT_HTML",
        "Returns a JSON object that specifies the HTML message to use for a notification."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/text_html")
    ),
    ("TEXT_PLAIN", FunctionInfo::new(
        "TEXT_PLAIN",
        "Returns a JSON object that specifies the plain text message to use for a notification."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/text_plain")
    ),
];

pub const NUMERIC_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("DIV0", FunctionInfo::new(
        "DIV0",
        "Performs division like the division operator (/), but returns 0 when the divisor is 0 (rather than reporting an error)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/div0")
    ),
    ("DIV0NULL", FunctionInfo::new(
        "DIV0NULL",
        "Performs division like the division operator (/), but returns 0 when the divisor is 0 or NULL (rather than reporting an error or returning NULL)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/div0null")
    ),
    ("MOD", FunctionInfo::new(
        "MOD",
        "Returns the remainder of input expr1 divided by input expr2."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/mod")
    ),
    ("SIGN", FunctionInfo::new(
        "SIGN",
        "Returns the sign of its argument."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sign")
    ),
    ("SQUARE", FunctionInfo::new(
        "SQUARE",
        "Returns the square of a numeric expression (i.e. a numeric expression multiplied by itself)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/square")
    ),
    ("TRUNCATE", FunctionInfo::new(
        "TRUNCATE",
        "Rounds the input expression down to the nearest (or equal) integer closer to zero, or to the nearest equal or smaller value with the specified number of places after the decimal point."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/trunc")
    ),
    ("WIDTH_BUCKET", FunctionInfo::new(
        "WIDTH_BUCKET",
        "Constructs equi-width histograms, in which the histogram range is divided into intervals of identical size, and returns the bucket number into which the value of an expression falls, after it has been evaluated."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/width_bucket")
    ),
];

pub const SEMISTRUCTURED_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("ARRAY_CONSTRUCT_COMPACT", FunctionInfo::new(
        "ARRAY_CONSTRUCT_COMPACT",
        "Returns an array constructed from zero, one, or more inputs; the constructed array omits any NULL input values."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/array_construct_compact")
    .with_subcategory("array")
    ),
    ("AS_ARRAY", FunctionInfo::new(
        "AS_ARRAY",
        "Casts a VARIANT value to an ARRAY value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_array")
    .with_subcategory("conversion")
    ),
    ("AS_BINARY", FunctionInfo::new(
        "AS_BINARY",
        "Casts a VARIANT value to a BINARY value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_binary")
    .with_subcategory("conversion")
    ),
    ("AS_BOOLEAN", FunctionInfo::new(
        "AS_BOOLEAN",
        "Casts a VARIANT value to a BOOLEAN value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_boolean")
    .with_subcategory("conversion")
    ),
    ("AS_CHAR", FunctionInfo::new(
        "AS_CHAR",
        "Casts a VARIANT value to a VARCHAR value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_char-varchar")
    .with_subcategory("conversion")
    ),
    ("AS_DATE", FunctionInfo::new(
        "AS_DATE",
        "Casts a VARIANT value to a DATE value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_date")
    .with_subcategory("conversion")
    ),
    ("AS_DECIMAL", FunctionInfo::new(
        "AS_DECIMAL",
        "Casts a VARIANT value to a fixed-point NUMBER value, with optional precision and scale."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_decimal-number")
    .with_subcategory("conversion")
    ),
    ("AS_DOUBLE", FunctionInfo::new(
        "AS_DOUBLE",
        "Casts a VARIANT value to a floating-point value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_double-real")
    .with_subcategory("conversion")
    ),
    ("AS_INTEGER", FunctionInfo::new(
        "AS_INTEGER",
        "Casts a VARIANT value to an INTEGER."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_integer")
    .with_subcategory("conversion")
    ),
    ("AS_NUMBER", FunctionInfo::new(
        "AS_NUMBER",
        "Casts a VARIANT value to a fixed-point NUMBER value, with optional precision and scale."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_decimal-number")
    .with_subcategory("conversion")
    ),
    ("AS_OBJECT", FunctionInfo::new(
        "AS_OBJECT",
        "Casts a VARIANT value to an OBJECT value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_object")
    .with_subcategory("conversion")
    ),
    ("AS_REAL", FunctionInfo::new(
        "AS_REAL",
        "Casts a VARIANT value to a floating-point value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_double-real")
    .with_subcategory("conversion")
    ),
    ("AS_TIME", FunctionInfo::new(
        "AS_TIME",
        "Casts a VARIANT value to a TIME value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_time")
    .with_subcategory("conversion")
    ),
    ("AS_TIMESTAMP_LTZ", FunctionInfo::new(
        "AS_TIMESTAMP_LTZ",
        "Casts a VARIANT value to the respective timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_timestamp")
    .with_subcategory("conversion")
    ),
    ("AS_TIMESTAMP_NTZ", FunctionInfo::new(
        "AS_TIMESTAMP_NTZ",
        "Casts a VARIANT value to the respective timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_timestamp")
    .with_subcategory("conversion")
    ),
    ("AS_TIMESTAMP_TZ", FunctionInfo::new(
        "AS_TIMESTAMP_TZ",
        "Casts a VARIANT value to the respective timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_timestamp")
    .with_subcategory("conversion")
    ),
    ("AS_VARCHAR", FunctionInfo::new(
        "AS_VARCHAR",
        "Casts a VARIANT value to a VARCHAR value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/as_char-varchar")
    .with_subcategory("conversion")
    ),
    ("CHECK_JSON", FunctionInfo::new(
        "CHECK_JSON",
        "Checks the validity of a JSON document."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/check_json")
    ),
    ("CHECK_XML", FunctionInfo::new(
        "CHECK_XML",
        "Checks the validity of an XML document."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/check_xml")
    ),
    ("EXPLAIN_JSON", FunctionInfo::new(
        "EXPLAIN_JSON",
        "This function converts an EXPLAIN plan from JSON to a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/explain_json")
    .with_subcategory("json")
    ),
    ("FILTER", FunctionInfo::new(
        "FILTER",
        "Filters an array based on the logic in a lambda expression."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/filter")
    ),
    ("GET_IGNORE_CASE", FunctionInfo::new(
        "GET_IGNORE_CASE",
        "Extracts a field value from an object; returns NULL if either of the arguments is NULL."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_ignore_case")
    ),
    ("IS_BINARY", FunctionInfo::new(
        "IS_BINARY",
        "Returns TRUE if its VARIANT argument contains a binary string value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_binary")
    ),
    ("IS_BOOLEAN", FunctionInfo::new(
        "IS_BOOLEAN",
        "Returns TRUE if its VARIANT argument contains a BOOLEAN value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_boolean")
    ),
    ("IS_CHAR", FunctionInfo::new(
        "IS_CHAR",
        "Returns TRUE if its VARIANT argument contains a string value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_char-varchar")
    ),
    ("IS_DATE", FunctionInfo::new(
        "IS_DATE",
        "Returns TRUE if its VARIANT argument contains a DATE value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_date-value")
    ),
    ("IS_DATE_VALUE", FunctionInfo::new(
        "IS_DATE_VALUE",
        "Returns TRUE if its VARIANT argument contains a DATE value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_date-value")
    ),
    ("IS_DECIMAL", FunctionInfo::new(
        "IS_DECIMAL",
        "Returns TRUE if its VARIANT argument contains a fixed-point number or integer value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_decimal")
    ),
    ("IS_DOUBLE", FunctionInfo::new(
        "IS_DOUBLE",
        "Returns TRUE if its VARIANT argument contains a floating-point number, fixed-point number, or integer value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_double-real")
    ),
    ("IS_INTEGER", FunctionInfo::new(
        "IS_INTEGER",
        "Returns TRUE if its VARIANT argument contains an integer value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_integer")
    ),
    ("IS_NULL_VALUE", FunctionInfo::new(
        "IS_NULL_VALUE",
        "Returns TRUE if its VARIANT argument is a JSON null value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_null_value")
    .with_subcategory("json")
    ),
    ("IS_REAL", FunctionInfo::new(
        "IS_REAL",
        "Returns TRUE if its VARIANT argument contains a floating-point number, fixed-point number, or integer value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_double-real")
    ),
    ("IS_TIME", FunctionInfo::new(
        "IS_TIME",
        "Verifies whether a VARIANT argument contains a TIME value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_time")
    ),
    ("IS_TIMESTAMP_LTZ", FunctionInfo::new(
        "IS_TIMESTAMP_LTZ",
        "Verifies whether a VARIANT argument contains the respective timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_timestamp")
    ),
    ("IS_TIMESTAMP_NTZ", FunctionInfo::new(
        "IS_TIMESTAMP_NTZ",
        "Verifies whether a VARIANT argument contains the respective timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_timestamp")
    ),
    ("IS_TIMESTAMP_TZ", FunctionInfo::new(
        "IS_TIMESTAMP_TZ",
        "Verifies whether a VARIANT argument contains the respective timestamp value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_timestamp")
    ),
    ("IS_VARCHAR", FunctionInfo::new(
        "IS_VARCHAR",
        "Returns TRUE if its VARIANT argument contains a string value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/is_char-varchar")
    ),
    ("JSON_EXTRACT_PATH_TEXT", FunctionInfo::new(
        "JSON_EXTRACT_PATH_TEXT",
        "Parses the first argument as a JSON string and returns the value of the element pointed to by the path in the second argument."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/json_extract_path_text")
    ),
    ("MAP_CAT", FunctionInfo::new(
        "MAP_CAT",
        "Returns the concatenatation of two MAPs."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/map_cat")
    .with_subcategory("map")
    ),
    ("MAP_CONTAINS_KEY", FunctionInfo::new(
        "MAP_CONTAINS_KEY",
        "Determines whether the specified MAP contains the specified key."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/map_contains_key")
    .with_subcategory("map")
    ),
    ("MAP_DELETE", FunctionInfo::new(
        "MAP_DELETE",
        "Returns a MAP based on an existing MAP with one or more keys removed.."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/map_delete")
    .with_subcategory("map")
    ),
    ("MAP_INSERT", FunctionInfo::new(
        "MAP_INSERT",
        "Returns a new MAP consisting of the input MAP with a new key-value pair inserted (an existing key updated with a new value)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/map_insert")
    .with_subcategory("map")
    ),
    ("MAP_PICK", FunctionInfo::new(
        "MAP_PICK",
        "Returns a new MAP containing the specified key-value pairs from an existing MAP."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/map_pick")
    .with_subcategory("map")
    ),
    ("MAP_SIZE", FunctionInfo::new(
        "MAP_SIZE",
        "Returns the size of a MAP."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/map_size")
    .with_subcategory("map")
    ),
    ("OBJECT_CONSTRUCT_KEEP_NULL", FunctionInfo::new(
        "OBJECT_CONSTRUCT_KEEP_NULL",
        "Returns an OBJECT constructed from the arguments that retains key-values pairs with NULL values."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/object_construct_keep_null")
    .with_subcategory("object")
    ),
    ("PARSE_XML", FunctionInfo::new(
        "PARSE_XML",
        "Interprets an input string as an XML document, producing an OBJECT value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/parse_xml")
    .with_subcategory("xml")
    ),
    ("REDUCE", FunctionInfo::new(
        "REDUCE",
        "Reduces an array to a single value based on the logic in a lambda expression."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/reduce")
    ),
    ("SEARCH_IP", FunctionInfo::new(
        "SEARCH_IP",
        "Searches for valid IPv4 addresses in specified character-string columns from one or more tables, including fields in VARIANT, OBJECT, and ARRAY columns."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/search_ip")
    ),
    ("STRIP_NULL_VALUE", FunctionInfo::new(
        "STRIP_NULL_VALUE",
        "Converts a JSON null value to a SQL NULL value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/strip_null_value")
    .with_subcategory("json")
    ),
    ("TRANSFORM", FunctionInfo::new(
        "TRANSFORM",
        "Transforms an array based on the logic in a lambda expression."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/transform")
    ),
    ("TYPEOF", FunctionInfo::new(
        "TYPEOF",
        "Returns the type of a value stored in a VARIANT column."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/typeof")
    ),
    ("XMLGET", FunctionInfo::new(
        "XMLGET",
        "Extracts an XML element object (often referred to as simply a tag) from the content of the outer XML element based on the name and instance number of the specified tag."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/xmlget")
    .with_subcategory("x,l")
    ),
];

pub const STRING_BINARY_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("BASE64_DECODE_BINARY", FunctionInfo::new(
        "BASE64_DECODE_BINARY",
        "Decodes a Base64-encoded string to a binary."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/base64_decode_binary")
    ),
    ("BASE64_DECODE_STRING", FunctionInfo::new(
        "BASE64_DECODE_STRING",
        "Decodes a Base64-encoded string to a string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/base64_decode_string")
    ),
    ("BASE64_ENCODE", FunctionInfo::new(
        "BASE64_ENCODE",
        "Encodes the input (string or binary) using Base64 encoding."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/base64_encode")
    ),
    ("CHAR", FunctionInfo::new(
        "CHAR",
        "Converts a Unicode code point (including 7-bit ASCII) into the character that matches the input Unicode."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/chr")
    ),
    ("CHARINDEX", FunctionInfo::new(
        "CHARINDEX",
        "Searches for the first occurrence of the first argument in the second argument and, if successful, returns the position (1-based) of the first argument in the second argument."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/charindex")
    ),
    ("CLASSIFY_TEXT", FunctionInfo::new(
        "CLASSIFY_TEXT",
        "Classifies free-form text into categories that you provide."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/classify_text-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("COLLATE", FunctionInfo::new(
        "COLLATE",
        "Returns a copy of the original string, but with the specified collation_specification property instead of the original collation_specification property."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/collate")
    ),
    ("COLLATION", FunctionInfo::new(
        "COLLATION",
        "Returns the collation specification of the expression."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/collation")
    ),
    ("COMPLETE", FunctionInfo::new(
        "COMPLETE",
        "Given a prompt, generates a response (completion) using your choice of supported language model."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/complete-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("COMPRESS", FunctionInfo::new(
        "COMPRESS",
        "Compresses the input string or binary value with a compression method."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/compress")
    ),
    ("COUNT_TOKENS", FunctionInfo::new(
        "COUNT_TOKENS",
        "Returns the number of tokens in a prompt for the large language model or the task-specific function specified in the argument."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/count_tokens-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("DECOMPRESS_BINARY", FunctionInfo::new(
        "DECOMPRESS_BINARY",
        "Decompresses the compressed BINARY input parameter."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/decompress_binary")
    ),
    ("DECOMPRESS_STRING", FunctionInfo::new(
        "DECOMPRESS_STRING",
        "Decompresses the compressed BINARY input parameter to a string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/decompress_string")
    ),
    ("EDITDISTANCE", FunctionInfo::new(
        "EDITDISTANCE",
        "Computes the Levenshtein distance between two input strings."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/editdistance")
    ),
    ("EMBED_TEXT_1024", FunctionInfo::new(
        "EMBED_TEXT_1024",
        "Creates a vector embedding of 1024 dimensions from text."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/embed_text_1024-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("EMBED_TEXT_768", FunctionInfo::new(
        "EMBED_TEXT_768",
        "Creates a vector embedding of 768 dimensions from English-language text."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/embed_text-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("ENDSWITH", FunctionInfo::new(
        "ENDSWITH",
        "Returns TRUE if the first expression ends with the second expression."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/endswith")
    ),
    ("EXTRACT_ANSWER", FunctionInfo::new(
        "EXTRACT_ANSWER",
        "Extracts an answer to a given question from a text document."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/extract_answer-snowflake-cortex")
    ),
    ("FINETUNE", FunctionInfo::new(
        "FINETUNE",
        "Cancels the specified fine-tuning job from the current schema."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/finetune-cancel")
    .with_subcategory("llm")
    ),
    ("FINETUNE", FunctionInfo::new(
        "FINETUNE",
        "Creates a fine-tuning job."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/finetune-create")
    .with_subcategory("llm")
    ),
    ("FINETUNE", FunctionInfo::new(
        "FINETUNE",
        "Describes the properties of a fine-tuning job."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/finetune-describe")
    .with_subcategory("llm")
    ),
    ("FINETUNE", FunctionInfo::new(
        "FINETUNE",
        "Lists the fine-tuning jobs for which you have access privileges."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/finetune-show")
    .with_subcategory("llm")
    ),
    ("FINETUNE", FunctionInfo::new(
        "FINETUNE",
        "This function lets you create and manage large language models customized for your specific task."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/finetune-snowflake-cortex")
    ),
    ("HEX_DECODE_BINARY", FunctionInfo::new(
        "HEX_DECODE_BINARY",
        "Decodes a hex-encoded string to a binary."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hex_decode_binary")
    ),
    ("HEX_DECODE_STRING", FunctionInfo::new(
        "HEX_DECODE_STRING",
        "Decodes a hex-encoded string to a string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hex_decode_string")
    ),
    ("HEX_ENCODE", FunctionInfo::new(
        "HEX_ENCODE",
        "Encodes the input using hexadecimal (also ‘hex’ or ‘base16’) encoding."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/hex_encode")
    ),
    ("ILIKE ANY", FunctionInfo::new(
        "ILIKE ANY",
        "Performs a case-insensitive comparison to match a string against any of one or more specified patterns."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/ilike_any")
    ),
    ("LEN", FunctionInfo::new(
        "LEN",
        "Returns the length of an input string or binary value."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/length")
    ),
    ("LIKE ALL", FunctionInfo::new(
        "LIKE ALL",
        "Performs a case-sensitive comparison to match a string against all of one or more specified patterns."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/like_all")
    ),
    ("LIKE ANY", FunctionInfo::new(
        "LIKE ANY",
        "Performs a case-sensitive comparison to match a string against any of one or more specified patterns."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/like_any")
    ),
    ("MD5_BINARY", FunctionInfo::new(
        "MD5_BINARY",
        "Returns a 16-byte BINARY value containing the 128-bit MD5 message digest."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/md5_binary")
    ),
    ("MD5_HEX", FunctionInfo::new(
        "MD5_HEX",
        "Returns a 32-character hex-encoded string containing the 128-bit MD5 message digest."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/md5")
    ),
    ("MD5_NUMBER_LOWER64", FunctionInfo::new(
        "MD5_NUMBER_LOWER64",
        "Calculates the 128-bit MD5 message digest, interprets it as a signed 128-bit big endian number, and returns the lower 64 bits of the number as an unsigned integer."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/md5_number_lower64")
    ),
    ("MD5_NUMBER_UPPER64", FunctionInfo::new(
        "MD5_NUMBER_UPPER64",
        "Calculates the 128-bit MD5 message digest, interprets it as a signed 128-bit big endian number, and returns the upper 64 bits of the number as an unsigned integer."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/md5_number_upper64")
    ),
    ("PARSE_DOCUMENT", FunctionInfo::new(
        "PARSE_DOCUMENT",
        "Returns the extracted content from a document on a Snowflake stage as an OBJECT that contains JSON-encoded objects as strings."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/parse_document-snowflake-cortex")
    ),
    ("PARSE_IP", FunctionInfo::new(
        "PARSE_IP",
        "Returns a JSON object consisting of all the components from a valid INET (Internet Protocol) or CIDR (Classless Internet Domain Routing) IPv4 or IPv6 string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/parse_ip")
    ),
    ("PARSE_URL", FunctionInfo::new(
        "PARSE_URL",
        "Returns a JSON object consisting of all the components (fragment, host, path, port, query, scheme) in a valid input URL/URI."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/parse_url")
    ),
    ("REGEXP_INSTR", FunctionInfo::new(
        "REGEXP_INSTR",
        "Returns the position of the specified occurrence of the regular expression pattern in the string subject."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/regexp_instr")
    .with_subcategory("regex")
    ),
    ("REGEXP_SUBSTR_ALL", FunctionInfo::new(
        "REGEXP_SUBSTR_ALL",
        "Returns an ARRAY that contains all substrings that match a regular expression within a string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/regexp_substr_all")
    .with_subcategory("regex")
    ),
    ("SEARCH", FunctionInfo::new(
        "SEARCH",
        "Searches character data (text) in specified columns from one or more tables, including fields in VARIANT, OBJECT, and ARRAY columns."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/search")
    ),
    ("SEARCH_PREVIEW", FunctionInfo::new(
        "SEARCH_PREVIEW",
        "Given a Cortex Search service name, and a query, returns a response from the specified service."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/search_preview-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("SENTIMENT", FunctionInfo::new(
        "SENTIMENT",
        "Returns a sentiment score for the given English-language input text."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sentiment-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("SHA1", FunctionInfo::new(
        "SHA1",
        "Returns a 40-character hex-encoded string containing the 160-bit SHA-1 message digest."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sha1")
    ),
    ("SHA1_BINARY", FunctionInfo::new(
        "SHA1_BINARY",
        "Returns a 20-byte binary containing the 160-bit SHA-1 message digest."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sha1_binary")
    ),
    ("SHA1_HEX", FunctionInfo::new(
        "SHA1_HEX",
        "Returns a 40-character hex-encoded string containing the 160-bit SHA-1 message digest."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sha1")
    ),
    ("SHA2", FunctionInfo::new(
        "SHA2",
        "Returns a hex-encoded string containing the N-bit SHA-2 message digest, where N is the specified output digest size."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sha2")
    ),
    ("SHA2_BINARY", FunctionInfo::new(
        "SHA2_BINARY",
        "Returns a binary containing the N-bit SHA-2 message digest, where N is the specified output digest size."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sha2_binary")
    ),
    ("SHA2_HEX", FunctionInfo::new(
        "SHA2_HEX",
        "Returns a hex-encoded string containing the N-bit SHA-2 message digest, where N is the specified output digest size."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/sha2")
    ),
    ("SOUNDEX", FunctionInfo::new(
        "SOUNDEX",
        "Returns a string that contains a phonetic representation of the input string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/soundex")
    ),
    ("SOUNDEX_P123", FunctionInfo::new(
        "SOUNDEX_P123",
        "Returns a string that contains a phonetic representation of the input string, and retains the Soundex code number for the second letter when the first and second letters use the same number."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/soundex_p123")
    ),
    ("SPACE", FunctionInfo::new(
        "SPACE",
        "Builds a string consisting of the specified number of blank spaces."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/space")
    ),
    ("SPLIT", FunctionInfo::new(
        "SPLIT",
        "Splits a given string with a given separator and returns the result in an array of strings."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/split")
    ),
    ("SPLIT_TEXT_RECURSIVE_CHARACTER", FunctionInfo::new(
        "SPLIT_TEXT_RECURSIVE_CHARACTER",
        "The SPLIT_TEXT_RECURSIVE_CHARACTER function splits a string into shorter stings, recursively, for preprocessing text to be used with text embedding or search indexing functions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/split_text_recursive_character-snowflake-cortex")
    ),
    ("STARTSWITH", FunctionInfo::new(
        "STARTSWITH",
        "Returns true if expr1 starts with expr2."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/startswith")
    ),
    ("STRTOK", FunctionInfo::new(
        "STRTOK",
        "Tokenizes a given string and returns the requested part."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/strtok")
    ),
    ("SUMMARIZE", FunctionInfo::new(
        "SUMMARIZE",
        "Summarizes the given English-language input text."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/summarize-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("TRY_BASE64_DECODE_BINARY", FunctionInfo::new(
        "TRY_BASE64_DECODE_BINARY",
        "A special version of BASE64_DECODE_BINARY that returns a NULL value if an error occurs during decoding."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_base64_decode_binary")
    ),
    ("TRY_BASE64_DECODE_STRING", FunctionInfo::new(
        "TRY_BASE64_DECODE_STRING",
        "A special version of BASE64_DECODE_STRING that returns a NULL value if an error occurs during decoding."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_base64_decode_string")
    ),
    ("TRY_COMPLETE", FunctionInfo::new(
        "TRY_COMPLETE",
        "Performs the same operation as the COMPLETE function but returns NULL instead of raising an error when the operation cannot be performed."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_complete-snowflake-cortex")
    .with_subcategory("llm")
    ),
    ("TRY_HEX_DECODE_BINARY", FunctionInfo::new(
        "TRY_HEX_DECODE_BINARY",
        "A special version of HEX_DECODE_BINARY that returns a NULL value if an error occurs during decoding."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_hex_decode_binary")
    ),
    ("TRY_HEX_DECODE_STRING", FunctionInfo::new(
        "TRY_HEX_DECODE_STRING",
        "A special version of HEX_DECODE_STRING that returns a NULL value if an error occurs during decoding."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/try_hex_decode_string")
    ),
    ("UNICODE", FunctionInfo::new(
        "UNICODE",
        "Returns the Unicode code point for the first Unicode character in a string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/unicode")
    ),
];

pub const SYSTEM_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("EXTRACT_SEMANTIC_CATEGORIES", FunctionInfo::new(
        "EXTRACT_SEMANTIC_CATEGORIES",
        "Returns a set of categories (semantic and privacy) for each supported column in the specified table or view."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/extract_semantic_categories")
    ),
    ("GET_ANACONDA_PACKAGES_REPODATA", FunctionInfo::new(
        "GET_ANACONDA_PACKAGES_REPODATA",
        "Returns a list of third-party packages that are available from Anaconda."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_anaconda_packages_repodata")
    ),
    ("GET_PYTHON_PROFILER_OUTPUT", FunctionInfo::new(
        "GET_PYTHON_PROFILER_OUTPUT",
        "Returns output containing a report generated by the Python code profiler."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_python_profiler_output")
    ),
    ("GET_QUERY_OPERATOR_STATS", FunctionInfo::new(
        "GET_QUERY_OPERATOR_STATS",
        "Returns statistics about individual query operators within a query that has completed."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_query_operator_stats")
    ),
    ("SHOW_PYTHON_PACKAGES_DEPENDENCIES", FunctionInfo::new(
        "SHOW_PYTHON_PACKAGES_DEPENDENCIES",
        "Returns a list of the dependencies and their versions for the Python packages that were specified."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/show_python_packages_dependencies")
    ),
    ("SYSTEM$ABORT_SESSION", FunctionInfo::new(
        "SYSTEM$ABORT_SESSION",
        "Aborts the specified session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_abort_session")
    ),
    ("SYSTEM$ABORT_TRANSACTION", FunctionInfo::new(
        "SYSTEM$ABORT_TRANSACTION",
        "Aborts the specified transaction, if it is running."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_abort_transaction")
    ),
    ("SYSTEM$ADD_EVENT", FunctionInfo::new(
        "SYSTEM$ADD_EVENT",
        "Add an event for trace."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_add_event")
    ),
    ("SYSTEM$ALLOWLIST", FunctionInfo::new(
        "SYSTEM$ALLOWLIST",
        "Returns hostnames and port numbers to add to your firewall’s allowed list so that you can access Snowflake from behind your firewall."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_allowlist")
    ),
    ("SYSTEM$ALLOWLIST_PRIVATELINK", FunctionInfo::new(
        "SYSTEM$ALLOWLIST_PRIVATELINK",
        "Returns hostnames and port numbers for AWS PrivateLink, Azure Private Link, and Google Cloud Private Service Connect deployments to add to your firewall’s allowed list so that you can access Snowflake from behind your firewall."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_allowlist_privatelink")
    ),
    ("SYSTEM$AUTHORIZE_PRIVATELINK", FunctionInfo::new(
        "SYSTEM$AUTHORIZE_PRIVATELINK",
        "Enables private connectivity to the Snowflake service for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_authorize_privatelink")
    ),
    ("SYSTEM$AUTHORIZE_STAGE_PRIVATELINK_ACCESS", FunctionInfo::new(
        "SYSTEM$AUTHORIZE_STAGE_PRIVATELINK_ACCESS",
        "Authorizes Snowflake to access the Microsoft Azure Private Endpoint for Azure private endpoints for internal stages for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_authorize_stage_privatelink_access")
    ),
    ("SYSTEM$AUTO_REFRESH_STATUS", FunctionInfo::new(
        "SYSTEM$AUTO_REFRESH_STATUS",
        "Returns the automated refresh status for an externally managed Iceberg table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_auto_refresh_status")
    ),
    ("SYSTEM$BEHAVIOR_CHANGE_BUNDLE_STATUS", FunctionInfo::new(
        "SYSTEM$BEHAVIOR_CHANGE_BUNDLE_STATUS",
        "Returns the status of the specified behavior change release bundle for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_behavior_change_bundle_status")
    ),
    ("SYSTEM$BLOCK_INTERNAL_STAGES_PUBLIC_ACCESS", FunctionInfo::new(
        "SYSTEM$BLOCK_INTERNAL_STAGES_PUBLIC_ACCESS",
        "Prevents all public traffic from accessing the internal stage of the current Snowflake account on Microsoft Azure."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_block_internal_stages_public_access")
    ),
    ("SYSTEM$CANCEL_ALL_QUERIES", FunctionInfo::new(
        "SYSTEM$CANCEL_ALL_QUERIES",
        "Cancels all active/running queries in the specified session."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_cancel_all_queries")
    ),
    ("SYSTEM$CANCEL_QUERY", FunctionInfo::new(
        "SYSTEM$CANCEL_QUERY",
        "Cancels the specified query (or statement) if it is currently active/running."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_cancel_query")
    ),
    ("SYSTEM$CLEANUP_DATABASE_ROLE_GRANTS", FunctionInfo::new(
        "SYSTEM$CLEANUP_DATABASE_ROLE_GRANTS",
        "Revokes privileges on dropped objects from the share and grants the database role to the share."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_cleanup_database_role_grants")
    ),
    ("SYSTEM$CLIENT_VERSION_INFO", FunctionInfo::new(
        "SYSTEM$CLIENT_VERSION_INFO",
        "Returns version information for Snowflake clients and drivers."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_client_version_info")
    ),
    ("SYSTEM$CLUSTERING_DEPTH", FunctionInfo::new(
        "SYSTEM$CLUSTERING_DEPTH",
        "Computes the average depth of the table according to the specified columns (or the clustering key defined for the table)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_clustering_depth")
    ),
    ("SYSTEM$CLUSTERING_INFORMATION", FunctionInfo::new(
        "SYSTEM$CLUSTERING_INFORMATION",
        "Returns clustering information, including average clustering depth, for a table based on one or more columns in the table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_clustering_information")
    ),
    ("SYSTEM$COMMIT_MOVE_ORGANIZATION_ACCOUNT", FunctionInfo::new(
        "SYSTEM$COMMIT_MOVE_ORGANIZATION_ACCOUNT",
        "Finalizes the process of moving an organization account from one region to another."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_commit_move_organization_account")
    ),
    ("SYSTEM$CONVERT_PIPES_SQS_TO_SNS", FunctionInfo::new(
        "SYSTEM$CONVERT_PIPES_SQS_TO_SNS",
        "Convert pipes using Amazon SQS (Simple Queue Service) notifications to the Amazon Simple Notification Service (SNS) service for an S3 bucket."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_convert_pipes_sqs_to_sns")
    ),
    ("SYSTEM$CREATE_BILLING_EVENT", FunctionInfo::new(
        "SYSTEM$CREATE_BILLING_EVENT",
        "Creates a billable event that tracks consumer usage of an installed monetized application."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_create_billing_event")
    ),
    ("SYSTEM$CREATE_BILLING_EVENTS", FunctionInfo::new(
        "SYSTEM$CREATE_BILLING_EVENTS",
        "Creates multiple billable events that track consumer usage of installed monetized applications."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_create_billing_events")
    ),
    ("SYSTEM$CURRENT_USER_TASK_NAME", FunctionInfo::new(
        "SYSTEM$CURRENT_USER_TASK_NAME",
        "Returns the name of the task currently executing when invoked from the statement or stored procedure defined by the task."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_current_user_task_name")
    ),
    ("SYSTEM$DATA_METRIC_SCAN", FunctionInfo::new(
        "SYSTEM$DATA_METRIC_SCAN",
        "Returns the rows identified by a data quality metric as containing data that failed a data quality check."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_data_metric_scan")
    ),
    ("SYSTEM$DEPROVISION_PRIVATELINK_ENDPOINT", FunctionInfo::new(
        "SYSTEM$DEPROVISION_PRIVATELINK_ENDPOINT",
        "Deprovisions a private connectivity endpoint in the Snowflake VPC or VNet to prevent Snowflake from connecting to an external service using private connectivity."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_deprovision_privatelink_endpoint")
    ),
    ("SYSTEM$DISABLE_BEHAVIOR_CHANGE_BUNDLE", FunctionInfo::new(
        "SYSTEM$DISABLE_BEHAVIOR_CHANGE_BUNDLE",
        "Disables the behavior changes included in the specified behavior change release bundle for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_disable_behavior_change_bundle")
    ),
    ("SYSTEM$DISABLE_DATABASE_REPLICATION", FunctionInfo::new(
        "SYSTEM$DISABLE_DATABASE_REPLICATION",
        "Disable replication for a primary database and any secondary databases linked to it."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_disable_database_replication")
    ),
    ("SYSTEM$DISABLE_PREVIEW_ACCESS", FunctionInfo::new(
        "SYSTEM$DISABLE_PREVIEW_ACCESS",
        "Disables access to open preview and private preview features."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_disable_preview_access")
    ),
    ("SYSTEM$ENABLE_BEHAVIOR_CHANGE_BUNDLE", FunctionInfo::new(
        "SYSTEM$ENABLE_BEHAVIOR_CHANGE_BUNDLE",
        "Enables behavior changes included in the specified behavior change release bundle for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_enable_behavior_change_bundle")
    ),
    ("SYSTEM$ENABLE_PREVIEW_ACCESS", FunctionInfo::new(
        "SYSTEM$ENABLE_PREVIEW_ACCESS",
        "Enables access to open preview features."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_enable_preview_access")
    ),
    ("SYSTEM$ESTIMATE_AUTOMATIC_CLUSTERING_COSTS", FunctionInfo::new(
        "SYSTEM$ESTIMATE_AUTOMATIC_CLUSTERING_COSTS",
        "Returns estimated costs associated with enabling Automatic Clustering for a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_estimate_automatic_clustering_costs")
    ),
    ("SYSTEM$ESTIMATE_QUERY_ACCELERATION", FunctionInfo::new(
        "SYSTEM$ESTIMATE_QUERY_ACCELERATION",
        "For a previously executed query, this function returns a JSON object that specifies if the query is eligible to benefit from the query acceleration service."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_estimate_query_acceleration")
    ),
    ("SYSTEM$ESTIMATE_SEARCH_OPTIMIZATION_COSTS", FunctionInfo::new(
        "SYSTEM$ESTIMATE_SEARCH_OPTIMIZATION_COSTS",
        "Returns the estimated costs of adding search optimization to a given table and configuring specific columns for search optimization."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_estimate_search_optimization_costs")
    ),
    ("SYSTEM$EXPLAIN_JSON_TO_TEXT", FunctionInfo::new(
        "SYSTEM$EXPLAIN_JSON_TO_TEXT",
        "This function converts EXPLAIN output from JSON to formatted text."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_explain_json_to_text")
    ),
    ("SYSTEM$EXPLAIN_PLAN_JSON", FunctionInfo::new(
        "SYSTEM$EXPLAIN_PLAN_JSON",
        "Given the text of a SQL statement, this function generates the EXPLAIN plan in JSON."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_explain_plan_json")
    ),
    ("SYSTEM$EXTERNAL_TABLE_PIPE_STATUS", FunctionInfo::new(
        "SYSTEM$EXTERNAL_TABLE_PIPE_STATUS",
        "Retrieves a JSON representation of the current refresh status for the internal (hidden) pipe object associated with an external table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_external_table_pipe_status")
    ),
    ("SYSTEM$FINISH_OAUTH_FLOW", FunctionInfo::new(
        "SYSTEM$FINISH_OAUTH_FLOW",
        "Sets the OAUTH_REFRESH_TOKEN parameter value of the secret passed as an argument in the SYSTEM$START_OAUTH_FLOW call that began the OAuth flow."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_finish_oauth_flow")
    ),
    ("SYSTEM$GENERATE_SAML_CSR", FunctionInfo::new(
        "SYSTEM$GENERATE_SAML_CSR",
        "Generates a certificate signing request (CSR) with the subject set to the subject of the certificate stored in the SAML2 integration and can specify the DN to be used in the CSR."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_generate_saml_csr")
    ),
    ("SYSTEM$GENERATE_SCIM_ACCESS_TOKEN", FunctionInfo::new(
        "SYSTEM$GENERATE_SCIM_ACCESS_TOKEN",
        "Returns a new SCIM access token that is valid for six months."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_generate_scim_access_token")
    ),
    ("SYSTEM$GET_AWS_SNS_IAM_POLICY", FunctionInfo::new(
        "SYSTEM$GET_AWS_SNS_IAM_POLICY",
        "Returns an AWS IAM policy statement that must be added to the Amazon SNS topic policy in order to grant the Amazon SQS messaging queue created by Snowflake to subscribe to the topic."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_aws_sns_iam_policy")
    ),
    ("SYSTEM$GET_CLASSIFICATION_RESULT", FunctionInfo::new(
        "SYSTEM$GET_CLASSIFICATION_RESULT",
        "Returns the classification result of the specified object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_classification_result")
    ),
    ("SYSTEM$GET_CMK_AKV_CONSENT_URL", FunctionInfo::new(
        "SYSTEM$GET_CMK_AKV_CONSENT_URL",
        "Returns a consent URL to the Azure Key Vault account related to customer-managed keys."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_akv_consent_url")
    ),
    ("SYSTEM$GET_CMK_CONFIG", FunctionInfo::new(
        "SYSTEM$GET_CMK_CONFIG",
        "Returns configuration information for use with customer-managed keys (CMKs) and Tri-Secret Secure."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_config")
    ),
    ("SYSTEM$GET_CMK_INFO", FunctionInfo::new(
        "SYSTEM$GET_CMK_INFO",
        "Returns a status about your customer-managed key (CMK) for use with Tri-Secret Secure."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_info")
    ),
    ("SYSTEM$GET_CMK_KMS_KEY_POLICY", FunctionInfo::new(
        "SYSTEM$GET_CMK_KMS_KEY_POLICY",
        "Returns an ARRAY containing a snippet of the AWS Key Management Service policy information related to customer-managed keys."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_cmk_kms_key_policy")
    ),
    ("SYSTEM$GET_COMPUTE_POOL_STATUS", FunctionInfo::new(
        "SYSTEM$GET_COMPUTE_POOL_STATUS",
        "Retrieves status of a compute pool."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_compute_pool_status")
    ),
    ("SYSTEM$GET_DIRECTORY_TABLE_STATUS", FunctionInfo::new(
        "SYSTEM$GET_DIRECTORY_TABLE_STATUS",
        "Returns a list of records that contain the directory table consistency status for stages in your account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_directory_table_status")
    ),
    ("SYSTEM$GET_GCP_KMS_CMK_GRANT_ACCESS_CMD", FunctionInfo::new(
        "SYSTEM$GET_GCP_KMS_CMK_GRANT_ACCESS_CMD",
        "Returns a Google Cloud gcloud command to obtain policy information for the Google Cloud Key Management Service for use with customer-managed keys."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_gcp_kms_cmk_grant_access_cmd")
    ),
    ("SYSTEM$GET_ICEBERG_TABLE_INFORMATION", FunctionInfo::new(
        "SYSTEM$GET_ICEBERG_TABLE_INFORMATION",
        "Returns the location of the root metadata file and status of the latest snapshot for an Apache Iceberg™ table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_iceberg_table_information")
    ),
    ("SYSTEM$GET_LOGIN_FAILURE_DETAILS", FunctionInfo::new(
        "SYSTEM$GET_LOGIN_FAILURE_DETAILS",
        "Returns a JSON object that represents an unsuccessful login attempt associated with External OAuth, SAML, or key pair authentication."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_login_failure_details")
    ),
    ("SYSTEM$GET_PREDECESSOR_RETURN_VALUE", FunctionInfo::new(
        "SYSTEM$GET_PREDECESSOR_RETURN_VALUE",
        "Retrieves the return value for the predecessor task in a task graph."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_predecessor_return_value")
    ),
    ("SYSTEM$GET_PREVIEW_ACCESS_STATUS", FunctionInfo::new(
        "SYSTEM$GET_PREVIEW_ACCESS_STATUS",
        "Determine if access to all preview features is enabled or disabled."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_preview_access_status")
    ),
    ("SYSTEM$GET_PRIVATELINK", FunctionInfo::new(
        "SYSTEM$GET_PRIVATELINK",
        "Verifies whether your current account is authorized for private connectivity to the Snowflake service."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink")
    ),
    ("SYSTEM$GET_PRIVATELINK_AUTHORIZED_ENDPOINTS", FunctionInfo::new(
        "SYSTEM$GET_PRIVATELINK_AUTHORIZED_ENDPOINTS",
        "Returns a list of the authorized endpoints for your current account to use with private connectivity to the Snowflake service."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink_authorized_endpoints")
    ),
    ("SYSTEM$GET_PRIVATELINK_CONFIG", FunctionInfo::new(
        "SYSTEM$GET_PRIVATELINK_CONFIG",
        "Returns a JSON representation of the Snowflake account information necessary to facilitate the self-service configuration of private connectivity to the Snowflake service or Snowflake internal stages."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink_config")
    ),
    ("SYSTEM$GET_PRIVATELINK_ENDPOINTS_INFO", FunctionInfo::new(
        "SYSTEM$GET_PRIVATELINK_ENDPOINTS_INFO",
        "Returns the status of all private connectivity endpoints that you provision."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_privatelink_endpoints_info")
    ),
    ("SYSTEM$GET_SERVICE_LOGS", FunctionInfo::new(
        "SYSTEM$GET_SERVICE_LOGS",
        "Retrieves local logs from a Snowpark Container Services service container."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_service_logs")
    ),
    ("SYSTEM$GET_SNOWFLAKE_PLATFORM_INFO", FunctionInfo::new(
        "SYSTEM$GET_SNOWFLAKE_PLATFORM_INFO",
        "Returns platform information for the cloud provider that hosts your Snowflake account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_snowflake_platform_info")
    ),
    ("SYSTEM$GET_TAG", FunctionInfo::new(
        "SYSTEM$GET_TAG",
        "Returns the tag value associated with the specified Snowflake object or column."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_tag")
    ),
    ("SYSTEM$GET_TAG_ALLOWED_VALUES", FunctionInfo::new(
        "SYSTEM$GET_TAG_ALLOWED_VALUES",
        "Returns a comma-separated list of string values that can be set on a supported object, or NULL to indicate the tag key does not have any specified string values and accepts all possible string values."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_tag_allowed_values")
    ),
    ("SYSTEM$GET_TAG_ON_CURRENT_COLUMN", FunctionInfo::new(
        "SYSTEM$GET_TAG_ON_CURRENT_COLUMN",
        "Returns the tag string value assigned to the column based upon the specified tag or NULL if a tag is not assigned to the specified column."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_tag_on_current_column")
    ),
    ("SYSTEM$GET_TAG_ON_CURRENT_TABLE", FunctionInfo::new(
        "SYSTEM$GET_TAG_ON_CURRENT_TABLE",
        "Returns the tag string value assigned to the table based upon the specified tag or NULL if a tag is not assigned to the specified table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_tag_on_current_table")
    ),
    ("SYSTEM$GET_TASK_GRAPH_CONFIG", FunctionInfo::new(
        "SYSTEM$GET_TASK_GRAPH_CONFIG",
        "Returns the value of the configuration string for the task currently executing when invoked from the statement or stored procedure defined by the task."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_get_task_graph_config")
    ),
    ("SYSTEM$GLOBAL_ACCOUNT_SET_PARAMETER", FunctionInfo::new(
        "SYSTEM$GLOBAL_ACCOUNT_SET_PARAMETER",
        "Enables replication and failover features for a specified account in an organization."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_global_account_set_parameter")
    ),
    ("SYSTEM$INITIATE_MOVE_ORGANIZATION_ACCOUNT", FunctionInfo::new(
        "SYSTEM$INITIATE_MOVE_ORGANIZATION_ACCOUNT",
        "Starts the process of moving an organization account to a new region."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_initiate_move_organization_account")
    ),
    ("SYSTEM$INTERNAL_STAGES_PUBLIC_ACCESS_STATUS", FunctionInfo::new(
        "SYSTEM$INTERNAL_STAGES_PUBLIC_ACCESS_STATUS",
        "Checks to see whether public IP addresses are allowed to access the internal stage of the current Snowflake account on Microsoft Azure."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_internal_stages_public_access_status")
    ),
    ("SYSTEM$IS_APPLICATION_INSTALLED_FROM_SAME_ACCOUNT", FunctionInfo::new(
        "SYSTEM$IS_APPLICATION_INSTALLED_FROM_SAME_ACCOUNT",
        "Shows if an app is installed on the same account as the application package it is based on."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_is_application_installed_from_same_account")
    ),
    ("SYSTEM$IS_APPLICATION_SHARING_EVENTS_WITH_PROVIDER", FunctionInfo::new(
        "SYSTEM$IS_APPLICATION_SHARING_EVENTS_WITH_PROVIDER",
        "Shows if event sharing is enabled."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_is_application_sharing_events_with_provider")
    ),
    ("SYSTEM$LAST_CHANGE_COMMIT_TIME", FunctionInfo::new(
        "SYSTEM$LAST_CHANGE_COMMIT_TIME",
        "Returns a token that can be used to detect whether a database table or view changed between two calls to the function."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_last_change_commit_time")
    ),
    ("SYSTEM$LINK_ACCOUNT_OBJECTS_BY_NAME", FunctionInfo::new(
        "SYSTEM$LINK_ACCOUNT_OBJECTS_BY_NAME",
        "Adds a global identifier to account objects in the target (current) account that were created using scripts and that match objects with the same names in the source account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_link_account_objects_by_name")
    ),
    ("SYSTEM$LIST_APPLICATION_RESTRICTED_FEATURES", FunctionInfo::new(
        "SYSTEM$LIST_APPLICATION_RESTRICTED_FEATURES",
        "Returns a JSON object containing a list of restricted features that the consumer has allowed a"
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_list_application_restricted_features")
    .with_subcategory("native-app")
    ),
    ("SYSTEM$LIST_ICEBERG_TABLES_FROM_CATALOG", FunctionInfo::new(
        "SYSTEM$LIST_ICEBERG_TABLES_FROM_CATALOG",
        "Lists tables in a remote"
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_list_iceberg_tables_from_catalog")
    .with_subcategory("iceberg")
    ),
    ("SYSTEM$LIST_NAMESPACES_FROM_CATALOG", FunctionInfo::new(
        "SYSTEM$LIST_NAMESPACES_FROM_CATALOG",
        "Lists the namespaces in a remote"
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_list_namespaces_from_catalog")
    .with_subcategory("iceberg")
    ),
    ("SYSTEM$MIGRATE_SAML_IDP_REGISTRATION", FunctionInfo::new(
        "SYSTEM$MIGRATE_SAML_IDP_REGISTRATION",
        "Migrates an existing SAML identity provider (i.e. IdP) configuration as defined by the account parameter SAML_IDENTITY_PROVIDER to a security integration."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_migrate_saml_idp_registration")
    ),
    ("SYSTEM$PIPE_FORCE_RESUME", FunctionInfo::new(
        "SYSTEM$PIPE_FORCE_RESUME",
        "Forces a pipe paused using ALTER PIPE to resume."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_pipe_force_resume")
    ),
    ("SYSTEM$PIPE_REBINDING_WITH_NOTIFICATION_CHANNEL", FunctionInfo::new(
        "SYSTEM$PIPE_REBINDING_WITH_NOTIFICATION_CHANNEL",
        "Retries the notification channel binding process when a replicated pipe has not been successfully bound to a notification channel during replication time."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_pipe_rebinding_with_notification_channel")
    ),
    ("SYSTEM$PIPE_STATUS", FunctionInfo::new(
        "SYSTEM$PIPE_STATUS",
        "Retrieves a JSON representation of the current status of a pipe."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_pipe_status")
    ),
    ("SYSTEM$PROVISION_PRIVATELINK_ENDPOINT", FunctionInfo::new(
        "SYSTEM$PROVISION_PRIVATELINK_ENDPOINT",
        "Provisions a private connectivity endpoint in the Snowflake VPC or VNet to enable Snowflake to connect to an external service using private connectivity."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_provision_privatelink_endpoint")
    ),
    ("SYSTEM$QUERY_REFERENCE", FunctionInfo::new(
        "SYSTEM$QUERY_REFERENCE",
        "Returns a query reference that you can pass to a stored procedure."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_query_reference")
    ),
    ("SYSTEM$REFERENCE", FunctionInfo::new(
        "SYSTEM$REFERENCE",
        "Returns a reference to an object (a table, view, or function)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_reference")
    ),
    ("SYSTEM$REGISTER_CMK_INFO", FunctionInfo::new(
        "SYSTEM$REGISTER_CMK_INFO",
        "Registers your customer-managed key (CMK) for use with Tri-Secret Secure."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_register_cmk_info")
    ),
    ("SYSTEM$RESTORE_PRIVATELINK_ENDPOINT", FunctionInfo::new(
        "SYSTEM$RESTORE_PRIVATELINK_ENDPOINT",
        "Restores a private connectivity endpoint in the Snowflake VPC or VNet to enable Snowflake to connect to an external service using private connectivity."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_restore_privatelink_endpoint")
    ),
    ("SYSTEM$REVOKE_PRIVATELINK", FunctionInfo::new(
        "SYSTEM$REVOKE_PRIVATELINK",
        "Disables private connectivity to the Snowflake service for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_revoke_privatelink")
    ),
    ("SYSTEM$REVOKE_STAGE_PRIVATELINK_ACCESS", FunctionInfo::new(
        "SYSTEM$REVOKE_STAGE_PRIVATELINK_ACCESS",
        "Revokes the authorization for Snowflake to access the Microsoft Azure Private Endpoint for Azure private endpoints for internal stages for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_revoke_stage_privatelink_access")
    ),
    ("SYSTEM$SEND_NOTIFICATIONS_TO_CATALOG", FunctionInfo::new(
        "SYSTEM$SEND_NOTIFICATIONS_TO_CATALOG",
        "Sends a notification to Snowflake Open Catalog to update Snowflake-managed Apache Iceberg™ tables in"
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_send_notifications_to_catalog")
    .with_subcategory("opencatalog")
    ),
    ("SYSTEM$SET_APPLICATION_RESTRICTED_FEATURE_ACCESS", FunctionInfo::new(
        "SYSTEM$SET_APPLICATION_RESTRICTED_FEATURE_ACCESS",
        "Enables a restricted feature for a"
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_set_application_restricted_feature_access")
    .with_subcategory("native-app")
    ),
    ("SYSTEM$SET_EVENT_SHARING_ACCOUNT_FOR_REGION", FunctionInfo::new(
        "SYSTEM$SET_EVENT_SHARING_ACCOUNT_FOR_REGION",
        "Sets the event account for a region."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_set_event_sharing_account_for_region")
    ),
    ("SYSTEM$SET_RETURN_VALUE", FunctionInfo::new(
        "SYSTEM$SET_RETURN_VALUE",
        "Explicitly sets the return value for a task."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_set_return_value")
    ),
    ("SYSTEM$SET_SPAN_ATTRIBUTES", FunctionInfo::new(
        "SYSTEM$SET_SPAN_ATTRIBUTES",
        "Sets attribute name and value associated with a span containing trace events."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_set_span_attributes")
    ),
    ("SYSTEM$SHOW_ACTIVE_BEHAVIOR_CHANGE_BUNDLES", FunctionInfo::new(
        "SYSTEM$SHOW_ACTIVE_BEHAVIOR_CHANGE_BUNDLES",
        "Returns an array of the currently available behavior change release bundles, the default state of each bundle, and the actual state of the bundle for the current account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_show_active_behavior_change_bundles")
    ),
    ("SYSTEM$SHOW_BUDGETS_IN_ACCOUNT", FunctionInfo::new(
        "SYSTEM$SHOW_BUDGETS_IN_ACCOUNT",
        "Returns the budgets in the account for which you have access privileges."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_show_budgets_in_account")
    ),
    ("SYSTEM$SHOW_EVENT_SHARING_ACCOUNTS", FunctionInfo::new(
        "SYSTEM$SHOW_EVENT_SHARING_ACCOUNTS",
        "Shows event accounts in a provider organization."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_show_event_sharing_accounts")
    ),
    ("SYSTEM$SHOW_MOVE_ORGANIZATION_ACCOUNT_STATUS", FunctionInfo::new(
        "SYSTEM$SHOW_MOVE_ORGANIZATION_ACCOUNT_STATUS",
        "Returns the status of an attempt to move an organization account."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_show_move_organization_account_status")
    ),
    ("SYSTEM$SHOW_OAUTH_CLIENT_SECRETS", FunctionInfo::new(
        "SYSTEM$SHOW_OAUTH_CLIENT_SECRETS",
        "Returns the client secrets in a string."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_show_oauth_client_secrets")
    ),
    ("SYSTEM$SNOWPIPE_STREAMING_UPDATE_CHANNEL_OFFSET_TOKEN", FunctionInfo::new(
        "SYSTEM$SNOWPIPE_STREAMING_UPDATE_CHANNEL_OFFSET_TOKEN",
        "Updates the offset token for a particular channel used by Snowpipe Streaming with a new offset token."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_snowpipe_streaming_update_channel_offset_token")
    ),
    ("SYSTEM$START_OAUTH_FLOW", FunctionInfo::new(
        "SYSTEM$START_OAUTH_FLOW",
        "Initiates the OAUTH client flow, returning a URL you use in a browser to complete the OAuth consent process."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_start_oauth_flow")
    ),
    ("SYSTEM$STREAM_BACKLOG", FunctionInfo::new(
        "SYSTEM$STREAM_BACKLOG",
        "Returns the set of table versions between the current offset for a specified stream and the current timestamp."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_stream_backlog")
    ),
    ("SYSTEM$STREAM_GET_TABLE_TIMESTAMP", FunctionInfo::new(
        "SYSTEM$STREAM_GET_TABLE_TIMESTAMP",
        "Returns the timestamp in nanoseconds of the latest table version at or before the current offset for the specified stream."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_stream_get_table_timestamp")
    ),
    ("SYSTEM$STREAM_HAS_DATA", FunctionInfo::new(
        "SYSTEM$STREAM_HAS_DATA",
        "Indicates whether a specified stream contains change data capture (CDC) records."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_stream_has_data")
    ),
    ("SYSTEM$TASK_DEPENDENTS_ENABLE", FunctionInfo::new(
        "SYSTEM$TASK_DEPENDENTS_ENABLE",
        "Recursively resumes a specified task and all its dependent tasks."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_task_dependents_enable")
    ),
    ("SYSTEM$TASK_RUNTIME_INFO", FunctionInfo::new(
        "SYSTEM$TASK_RUNTIME_INFO",
        "Returns information about the current task run."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_task_runtime_info")
    ),
    ("SYSTEM$TYPEOF", FunctionInfo::new(
        "SYSTEM$TYPEOF",
        "Returns a string representing the SQL data type associated with an expression."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_typeof")
    ),
    ("SYSTEM$UNBLOCK_INTERNAL_STAGES_PUBLIC_ACCESS", FunctionInfo::new(
        "SYSTEM$UNBLOCK_INTERNAL_STAGES_PUBLIC_ACCESS",
        "Allows traffic from public IP addresses to access the internal stage of the current Snowflake account on Microsoft Azure."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_unblock_internal_stages_public_access")
    ),
    ("SYSTEM$UNSET_EVENT_SHARING_ACCOUNT_FOR_REGION", FunctionInfo::new(
        "SYSTEM$UNSET_EVENT_SHARING_ACCOUNT_FOR_REGION",
        "Unsets the events account for a region."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_unset_event_sharing_account_for_region")
    ),
    ("SYSTEM$USER_TASK_CANCEL_ONGOING_EXECUTIONS", FunctionInfo::new(
        "SYSTEM$USER_TASK_CANCEL_ONGOING_EXECUTIONS",
        "Aborts a run of the specified task that the system has already started to process (i.e. a run with an EXECUTING state in the TASK_HISTORY output)."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_user_task_cancel_ongoing_executions")
    ),
    ("SYSTEM$VALIDATE_STORAGE_INTEGRATION", FunctionInfo::new(
        "SYSTEM$VALIDATE_STORAGE_INTEGRATION",
        "Validates the configuration for a specified storage integration."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_validate_storage_integration")
    ),
    ("SYSTEM$VERIFY_CMK_INFO", FunctionInfo::new(
        "SYSTEM$VERIFY_CMK_INFO",
        "Verifies your customer-managed key (CMK) configuration and returns a message about the registered CMK."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_verify_cmk_info")
    ),
    ("SYSTEM$VERIFY_EXTERNAL_OAUTH_TOKEN", FunctionInfo::new(
        "SYSTEM$VERIFY_EXTERNAL_OAUTH_TOKEN",
        "Determines whether your External OAuth access token is valid or has expired and needs to be regenerated."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_verify_ext_oauth_token")
    ),
    ("SYSTEM$VERIFY_EXTERNAL_VOLUME", FunctionInfo::new(
        "SYSTEM$VERIFY_EXTERNAL_VOLUME",
        "Verifies the configuration for a specified external volume."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_verify_external_volume")
    ),
    ("SYSTEM$WAIT", FunctionInfo::new(
        "SYSTEM$WAIT",
        "Waits for the specified amount of time before proceeding."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_wait")
    ),
    ("SYSTEM$WAIT_FOR_SERVICES", FunctionInfo::new(
        "SYSTEM$WAIT_FOR_SERVICES",
        "Waits for one or more Snowpark Container Services services to reach the READY state (or becomes upgraded) before returning."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/system_wait_for_services")
    ),
];

pub const TABLE_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("CORTEX_SEARCH_DATA_SCAN", FunctionInfo::new(
        "CORTEX_SEARCH_DATA_SCAN",
        "This table function returns the data indexed by a Cortex Search service, including the columns defined in the source query and the computed vector embeddings for the search column."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/cortex_search_data_scan")
    ),
    ("GENERATOR", FunctionInfo::new(
        "GENERATOR",
        "Creates rows of data based either on a specified number of rows, a specified generation period (in seconds), or both."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/generator")
    ),
    ("GET_LINEAGE", FunctionInfo::new(
        "GET_LINEAGE",
        "Given a Snowflake object, returns data lineage information upstream or downstream from that object."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_lineage-snowflake-core")
    ),
    ("GET_OBJECT_REFERENCES", FunctionInfo::new(
        "GET_OBJECT_REFERENCES",
        "Returns a list of objects that a specified object references."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/get_object_references")
    ),
    ("INFER_SCHEMA", FunctionInfo::new(
        "INFER_SCHEMA",
        "Automatically detects the file metadata schema in a set of staged data files that contain semi-structured data and retrieves the column definitions."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/infer_schema")
    ),
    ("REST_EVENT_HISTORY", FunctionInfo::new(
        "REST_EVENT_HISTORY",
        "Returns a list of SCIM REST API requests made to Snowflake over a specified time interval."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/rest_event_history")
    ),
    ("RESULT_SCAN", FunctionInfo::new(
        "RESULT_SCAN",
        "Returns the result set of a previous command (within 24 hours of when you executed the query) as if the result was a table."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/result_scan")
    ),
    ("SPLIT_TO_TABLE", FunctionInfo::new(
        "SPLIT_TO_TABLE",
        "This table function splits a string (based on a specified delimiter) and flattens the results into rows."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/split_to_table")
    .with_subcategory("string")
    ),
    ("STRTOK_SPLIT_TO_TABLE", FunctionInfo::new(
        "STRTOK_SPLIT_TO_TABLE",
        "Tokenizes a string with the given set of delimiters and flattens the results into rows."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/strtok_split_to_table")
    .with_subcategory("string")
    ),
    ("TO_QUERY", FunctionInfo::new(
        "TO_QUERY",
        "Returns a result set based on SQL text and an optional set of arguments that are passed to the SQL text if it is parameterized."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/to_query")
    ),
    ("VALIDATE", FunctionInfo::new(
        "VALIDATE",
        "Validates the files loaded in a past execution of the COPY INTO <table> command and returns all the errors encountered during the load, rather than just the first error."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/validate")
    ),
];

pub const VECTOR_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    (
        "VECTOR_COSINE_SIMILARITY",
        FunctionInfo::new(
            "VECTOR_COSINE_SIMILARITY",
            "Computes the cosine similarity between two vectors.",
        )
        .with_docs(
            "https://docs.snowflake.com/en/sql-reference/functions/vector_cosine_similarity",
        ),
    ),
    (
        "VECTOR_INNER_PRODUCT",
        FunctionInfo::new(
            "VECTOR_INNER_PRODUCT",
            "Computes the inner product of two vectors.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/vector_inner_product"),
    ),
    (
        "VECTOR_L1_DISTANCE",
        FunctionInfo::new(
            "VECTOR_L1_DISTANCE",
            "Computes the L1 distance between two vectors.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/vector_l1_distance"),
    ),
    (
        "VECTOR_L2_DISTANCE",
        FunctionInfo::new(
            "VECTOR_L2_DISTANCE",
            "Computes the L2 distance between two vectors.",
        )
        .with_docs("https://docs.snowflake.com/en/sql-reference/functions/vector_l2_distance"),
    ),
];

pub const WINDOW_FUNCTIONS: &[(&str, FunctionInfo)] = &[
    ("CONDITIONAL_CHANGE_EVENT", FunctionInfo::new(
        "CONDITIONAL_CHANGE_EVENT",
        "Returns a window event number for each row within a window partition when the value of the argument expr1 in the current row is different from the value of expr1 in the previous row."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/conditional_change_event")
    ),
    ("CONDITIONAL_TRUE_EVENT", FunctionInfo::new(
        "CONDITIONAL_TRUE_EVENT",
        "Returns a window event number for each row within a window partition based on the result of the boolean argument expr1."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/conditional_true_event")
    ),
    ("RATIO_TO_REPORT", FunctionInfo::new(
        "RATIO_TO_REPORT",
        "Returns the ratio of a value within a group to the sum of the values within the group."
    )
    .with_docs("https://docs.snowflake.com/en/sql-reference/functions/ratio_to_report")
    ),
];
