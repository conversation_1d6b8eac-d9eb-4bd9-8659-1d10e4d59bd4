//! Traits and structures for generating test data.
//!
//! This module provides the core functionality for generating test data based on templates.
//! It defines the `Generator` trait for creating data and the `WithCount` helper for
//! generating multiple instances of a type.

use serde::{Deserialize, Serialize};
use std::marker::PhantomData;

/// A trait for types that can generate instances of `T`.
///
/// Implement this trait to define how to generate test data of a specific type.
/// The `generate` method is called with an index parameter that can be used to
/// ensure uniqueness or for reference in the generation process.
pub trait Generator<T> {
    /// Generates a single instance of `T`.
    ///
    /// # Arguments
    /// * `index` - A unique index of entity in vec to be used during generation
    fn generate(&self, index: usize) -> T;
}

/// A wrapper that generates multiple instances of a type using a template generator.
///
/// This structure combines a count with a generator to produce a vector of items.
/// It's particularly useful when you need to generate multiple similar items.
#[derive(Debug, Serialize, Deserialize, Default, PartialEq, Eq)]
pub struct WithCount<T, G>
where
    G: Generator<T>,
{
    /// The number of items to generate
    count: usize,
    /// The generator used to create each item
    template: G,
    #[serde(skip)]
    _marker: PhantomData<T>,
}

impl<T, G> WithCount<T, G>
where
    G: Generator<T>,
{
    /// Creates a new `WithCount` instance.
    ///
    /// # Arguments
    /// * `count` - The number of items to generate
    /// * `template` - The generator to use for creating items
    #[must_use]
    pub const fn new(count: usize, template: G) -> Self {
        Self {
            count,
            template,
            _marker: PhantomData,
        }
    }

    /// Generates a vector of items using the template generator.
    ///
    /// # Arguments
    /// * `_index` - A context index that can be used during generation
    ///
    /// # Returns
    /// A vector containing `count` items generated by the template
    pub fn vec_with_count(&self, _index: usize) -> Vec<T> {
        (0..self.count).map(|i| self.template.generate(i)).collect()
    }
}
