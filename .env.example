# Shared settings (if no env vars are used, it will default to the first option (memory) with the shared settings)
# Iceberg Catalog settings
# Set to your catalog url
CATALOG_URL=http://127.0.0.1:3000
# Optional: CORS settings
CORS_ENABLED=true
CORS_ALLOW_ORIGIN=http://127.0.0.1:8080

# Option 1 (Memory)
# SlateDB storage settings
OBJECT_STORE_BACKEND=memory

# Option 2 (File)
# SlateDB storage settings
#OBJECT_STORE_BACKEND=file
#FILE_STORAGE_PATH=storage
#SLATEDB_PREFIX=state

# Option 3 (S3)
# SlateDB storage settings
#OBJECT_STORE_BACKEND=s3
# Optional: AWS S3 storage (leave blank if using local storage)
#AWS_ACCESS_KEY_ID="<your_aws_access_key_id>"
#AWS_SECRET_ACCESS_KEY="<your_aws_secret_access_key>"
#AWS_REGION="<your_aws_region>"
#S3_BUCKET="<your_s3_bucket>"
#S3_ALLOW_HTTP=
